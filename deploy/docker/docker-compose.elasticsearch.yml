include:
  - path: docker-compose.middleware.yml
services:
  elasticsearch:
    container_name: refly_elasticsearch
    image: reflyai/elasticsearch:7.10.2
    ports:
      - 39200:9200
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    restart: always
    healthcheck:
      test: ['CMD-SHELL', 'curl -s http://localhost:9200 >/dev/null || exit 1']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s
    networks:
      - refly_network
  api:
    extends:
      file: docker-compose.yml
      service: api
    depends_on:
      db:
        condition: service_healthy
      minio:
        condition: service_healthy
      redis:
        condition: service_healthy
      qdrant:
        condition: service_healthy
      elasticsearch:
        condition: service_healthy
    environment:
      - FULLTEXT_SEARCH_BACKEND=elasticsearch
      - ELASTICSEARCH_URL=http://elasticsearch:9200
  web:
    extends:
      file: docker-compose.yml
      service: web
