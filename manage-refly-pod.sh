#!/bin/bash

# Refly Pod 管理脚本

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

POD_NAME="refly-pod"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示状态
show_status() {
    log_info "Refly Pod 状态:"
    echo ""
    
    if podman pod exists "$POD_NAME"; then
        echo "Pod 信息:"
        podman pod ps --filter name="$POD_NAME"
        echo ""
        echo "容器信息:"
        podman ps --pod --filter pod="$POD_NAME"
    else
        log_warning "Pod '$POD_NAME' 不存在"
    fi
}

# 启动服务
start_service() {
    log_info "启动 Refly Pod..."
    
    if podman pod exists "$POD_NAME"; then
        podman pod start "$POD_NAME"
        log_success "Pod 启动完成"
    else
        log_error "Pod '$POD_NAME' 不存在，请先运行部署脚本"
        exit 1
    fi
}

# 停止服务
stop_service() {
    log_info "停止 Refly Pod..."
    
    if podman pod exists "$POD_NAME"; then
        podman pod stop "$POD_NAME"
        log_success "Pod 停止完成"
    else
        log_warning "Pod '$POD_NAME' 不存在"
    fi
}

# 重启服务
restart_service() {
    log_info "重启 Refly Pod..."
    
    if podman pod exists "$POD_NAME"; then
        podman pod restart "$POD_NAME"
        log_success "Pod 重启完成"
    else
        log_error "Pod '$POD_NAME' 不存在，请先运行部署脚本"
        exit 1
    fi
}

# 删除服务
remove_service() {
    log_warning "这将删除 Refly Pod 和所有容器（数据将保留）"
    read -p "确认删除？(y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "删除 Refly Pod..."
        
        if podman pod exists "$POD_NAME"; then
            podman pod stop "$POD_NAME" 2>/dev/null || true
            podman pod rm "$POD_NAME"
            log_success "Pod 删除完成"
        else
            log_warning "Pod '$POD_NAME' 不存在"
        fi
    else
        log_info "取消删除操作"
    fi
}

# 查看日志
show_logs() {
    local container_name="$1"
    local follow_flag="$2"
    
    if [ -z "$container_name" ]; then
        log_info "可用的容器:"
        podman ps --pod --filter pod="$POD_NAME" --format "table {{.Names}}\t{{.Status}}"
        echo ""
        log_info "使用方法: $0 logs <容器名> [--follow]"
        return
    fi
    
    if ! podman container exists "$container_name"; then
        log_error "容器 '$container_name' 不存在"
        return 1
    fi
    
    log_info "显示 $container_name 日志..."
    
    if [ "$follow_flag" = "--follow" ] || [ "$follow_flag" = "-f" ]; then
        podman logs -f "$container_name"
    else
        podman logs --tail 50 "$container_name"
    fi
}

# 进入容器
exec_container() {
    local container_name="$1"
    local command="${2:-/bin/sh}"
    
    if [ -z "$container_name" ]; then
        log_info "可用的容器:"
        podman ps --pod --filter pod="$POD_NAME" --format "table {{.Names}}\t{{.Status}}"
        echo ""
        log_info "使用方法: $0 exec <容器名> [命令]"
        return
    fi
    
    if ! podman container exists "$container_name"; then
        log_error "容器 '$container_name' 不存在"
        return 1
    fi
    
    local status=$(podman inspect "$container_name" --format "{{.State.Status}}")
    if [ "$status" != "running" ]; then
        log_error "容器 '$container_name' 未运行 (状态: $status)"
        return 1
    fi
    
    log_info "进入容器 $container_name..."
    podman exec -it "$container_name" "$command"
}

# 备份数据
backup_data() {
    local backup_dir="./refly-backup-$(date +%Y%m%d_%H%M%S)"
    
    log_info "创建数据备份到 $backup_dir..."
    mkdir -p "$backup_dir"
    
    if [ -d "./refly-data" ]; then
        cp -r ./refly-data "$backup_dir/"
        log_success "数据备份完成: $backup_dir"
    else
        log_error "数据目录 './refly-data' 不存在"
        return 1
    fi
    
    if [ -d "./refly-config" ]; then
        cp -r ./refly-config "$backup_dir/"
        log_success "配置备份完成: $backup_dir"
    fi
}

# 更新镜像
update_images() {
    log_info "更新 Refly 镜像..."
    
    local images=(
        "reflyai/refly-api:latest"
        "reflyai/refly-web:latest"
        "registry.cn-hangzhou.aliyuncs.com/library/postgres:16-alpine"
        "registry.cn-hangzhou.aliyuncs.com/library/redis:7-alpine"
        "registry.cn-hangzhou.aliyuncs.com/library/minio:RELEASE.2025-01-20T14-49-07Z"
        "qdrant/qdrant:v1.13.1"
        "searxng/searxng:latest"
    )
    
    for image in "${images[@]}"; do
        log_info "更新镜像: $image"
        podman pull "$image"
    done
    
    log_success "镜像更新完成"
    log_warning "请重启 Pod 以使用新镜像: $0 restart"
}

# 显示帮助信息
show_help() {
    echo "Refly Pod 管理脚本"
    echo ""
    echo "用法:"
    echo "  $0 status                    显示 Pod 和容器状态"
    echo "  $0 start                     启动 Pod"
    echo "  $0 stop                      停止 Pod"
    echo "  $0 restart                   重启 Pod"
    echo "  $0 remove                    删除 Pod（保留数据）"
    echo "  $0 logs [容器名] [--follow]   查看容器日志"
    echo "  $0 exec <容器名> [命令]       进入容器执行命令"
    echo "  $0 backup                    备份数据和配置"
    echo "  $0 update                    更新镜像"
    echo "  $0 help                      显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 logs refly-api            查看 API 服务日志"
    echo "  $0 logs refly-api --follow   实时跟踪 API 服务日志"
    echo "  $0 exec refly-postgres psql -U refly -d refly"
    echo ""
}

# 主函数
main() {
    case "${1:-}" in
        status|st)
            show_status
            ;;
        start)
            start_service
            ;;
        stop)
            stop_service
            ;;
        restart)
            restart_service
            ;;
        remove|rm)
            remove_service
            ;;
        logs)
            show_logs "$2" "$3"
            ;;
        exec)
            exec_container "$2" "$3"
            ;;
        backup)
            backup_data
            ;;
        update)
            update_images
            ;;
        help|--help|-h)
            show_help
            ;;
        "")
            show_status
            ;;
        *)
            log_error "未知命令: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
