name: End-to-End Tests

on:
  workflow_dispatch:

jobs:
  e2e:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false # https://github.com/cypress-io/github-action/issues/48
      matrix:
        containers: [1, 2] # Uses 2 parallel instances
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - uses: pnpm/action-setup@v4
        with:
          run_install: false

      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'pnpm'

      - name: Get Cypress version
        id: cypress-version
        run: echo "version=$(node -p "require('./package.json').devDependencies.cypress")" >> $GITHUB_OUTPUT

      - name: Cache Cypress Binary
        uses: actions/cache@v4
        id: cypress-binary-cache
        with:
          path: ~/.cache/Cypress
          key: cypress-binary-${{ runner.os }}-${{ steps.cypress-version.outputs.version }}
          restore-keys: |
            cypress-binary-${{ runner.os }}-

      - name: Install Dependencies
        run: pnpm install

      - name: Install Cypress
        run: pnpm cy:install

      - name: Start Middleware Containers
        run: |
          docker compose -f deploy/docker/docker-compose.middleware.yml up -d
          docker ps

      - name: Install yq
        run: |
          wget https://github.com/mikefarah/yq/releases/latest/download/yq_linux_amd64 -O /usr/local/bin/yq
          chmod +x /usr/local/bin/yq

      - name: Wait for Middleware Containers to be Healthy
        run: |
          CONTAINERS=$(yq e '.services | map(.container_name) | join(" ")' deploy/docker/docker-compose.middleware.yml)
          bash scripts/wait-for-container-health.sh $CONTAINERS
          docker ps

      - name: E2E Tests
        uses: cypress-io/github-action@v6
        with:
          browser: chrome
          install: false
          record: true
          parallel: true
          build: pnpm copy-env build
          start: pnpm start
          wait-on: 'http://localhost:5700,http://localhost:5800'
        env:
          VITE_API_URL: 'http://localhost:5800'
          VITE_COLLAB_URL: 'http://localhost:5801'
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NODE_ENV: 'ci'
          NODE_OPTIONS: '--max_old_space_size=8192'
          MOCK_LLM_RESPONSE: true
          TURBO_TEAM: ${{ secrets.TURBO_TEAM }}
          TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
          CYPRESS_BASE_URL: 'http://localhost:5700'
          CYPRESS_RECORD_KEY: ${{ secrets.CYPRESS_RECORD_KEY }}
          COMMIT_INFO_MESSAGE: ${{ github.event.pull_request.title }}
          COMMIT_INFO_SHA: ${{ github.event.pull_request.head.sha }}
