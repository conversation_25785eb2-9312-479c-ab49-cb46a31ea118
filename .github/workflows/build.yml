name: Build

on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
      web_deploy_project:
        required: false
        type: string

jobs:
  build:
    name: Build
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - uses: pnpm/action-setup@v4
        with:
          run_install: false

      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install

      - name: Check with Biome
        run: pnpm check

      - name: Build
        run: pnpm build
        env:
          NODE_OPTIONS: '--max_old_space_size=8192'
          TURBO_TEAM: ${{ secrets.TURBO_TEAM }}
          TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
          VITE_ENFORCE_TYPE_CHECK: 'true'
