name: Deploy API To Prod

on:
  workflow_dispatch:

jobs:
  deploy:
    name: Deploy API
    runs-on: ubuntu-latest
    if: github.repository == 'refly-ai/refly'
    steps:
      - name: Install kubectl
        uses: azure/setup-kubectl@v4

      - name: Configure kubectl
        run: |
          echo "${{ secrets.KUBE_CONFIG }}" > ./kubeconfig
          chmod 600 kubeconfig
          echo "KUBECONFIG=./kubeconfig" >> $GITHUB_ENV

      - name: Update Kubernetes deployment
        run: |
          kubectl set image deployment/refly-api refly-api=reflyai/refly-api:${{ github.sha }} --record -n refly-app

      - name: Wait for deployment to complete
        run: |
          kubectl rollout status deployment/refly-api --timeout=900s -n refly-app
