name: Deploy Web To Staging

on:
  workflow_dispatch:

jobs:
  deploy:
    name: Deploy Web
    runs-on: ubuntu-latest
    if: github.repository == 'refly-ai/refly'
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - uses: pnpm/action-setup@v4
        with:
          run_install: false

      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install

      - name: Build
        run: pnpm build:web
        env:
          NODE_OPTIONS: '--max_old_space_size=8192'
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
          TURBO_TEAM: ${{ secrets.TURBO_TEAM }}
          TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
          VITE_API_URL: https://staging-api.refly.ai
          VITE_COLLAB_URL: https://staging-collab.refly.ai
          VITE_SUBSCRIPTION_ENABLED: true
          VITE_CANVAS_TEMPLATE_ENABLED: true
          VITE_SENTRY_ENABLED: true
          VITE_SENTRY_DSN: ${{ secrets.SENTRY_DSN }}
          VITE_STATSIG_CLIENT_KEY: ${{ secrets.STATSIG_CLIENT_KEY }}
          VITE_GTAG_ID: ${{ secrets.GTAG_ID }}
          VITE_RUNTIME: web
          VITE_STATIC_PUBLIC_ENDPOINT: https://static.refly.ai
          VITE_STATIC_PRIVATE_ENDPOINT: https://staging-api.refly.ai/v1/misc

      - name: Deploy web to Cloudflare Pages
        uses: cloudflare/pages-action@v1.5.0
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          projectName: refly-app-staging
          branch: main
          workingDirectory: apps/web
          directory: dist
