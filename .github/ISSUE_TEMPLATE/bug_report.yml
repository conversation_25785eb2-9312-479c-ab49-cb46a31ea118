name: "🐛 Bug Report"
description: Report a bug to help us improve
labels:
  - bug
body:
  - type: checkboxes
    attributes:
      label: Self Checks
      description: "To make sure we get to you in time, please check the following :)"
      options:
        - label: I have searched for existing issues [search for existing issues](https://github.com/refly-ai/refly/issues), including closed ones.
          required: true
        - label: I confirm that I am using English to submit this report (我已阅读并同意 [Language Policy](https://github.com/refly-ai/refly/discussions)).
          required: true
        - label: "Please do not modify this template :) and fill in all the required fields."
      required: true
  - type: dropdown
    attributes:
      label: Affected Area
      description: Which area of Refly is affected by this bug?
      options:
        - Multi-threaded Dialogues
        - AI-Powered Capabilities (Web Search, Knowledge Base Search)
        - Context Memory & References
        - Knowledge Base Integration & RAG
        - Quotes & Citations
        - AI Document Editing & WYSIWYG
        - Free-form Canvas Interface
        - Other
    validations:
      required: true
  - type: textarea
    attributes:
      label: Current Behavior
      description: A clear description of what the bug is and how it manifests.
      placeholder: When I try to use the multi-threaded dialogue feature, X happens instead of Y...
    validations:
      required: true
  - type: textarea
    attributes:
      label: Expected Behavior
      description: A clear description of what you expected to happen.
    validations:
      required: true
  - type: textarea
    attributes:
      label: Steps To Reproduce
      description: Steps to reproduce the behavior
      placeholder: |
        1. Go to '...'
        2. Click on '....'
        3. Scroll down to '....'
        4. See error
    validations:
      required: true
  - type: textarea
    attributes:
      label: Environment
      description: |
        Examples:
          - **OS**: macOS 13.1
          - **Browser**: Chrome 108.0.5359.124
          - **Refly Version**: v0.2.3
      value: |
        - OS:
        - Browser:
        - Refly Version:
      render: markdown
    validations:
      required: true
  - type: textarea
    attributes:
      label: Additional Context
      description: Add any other context about the problem here (screenshots, logs, etc.)
    validations:
      required: false
