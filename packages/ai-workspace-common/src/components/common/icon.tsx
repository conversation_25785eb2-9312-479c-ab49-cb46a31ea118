import {
  HiOutlineSquare3Stack3D,
  HiOutlineSparkles,
  HiMiniPlayCircle,
  HiOutlineCircleStack,
  HiSquare3Stack3D,
  HiDocumentText,
  HiPlus,
  HiMinus,
  HiSparkles,
  HiOutlineHome,
  HiOutlineFilm,
  HiOutlineSpeakerWave,
} from 'react-icons/hi2';
import { IoIosMore } from 'react-icons/io';
import { IoLanguage } from 'react-icons/io5';
import { HiOutlineReply } from 'react-icons/hi';
import { FiCode } from 'react-icons/fi';
import {
  LuSearch,
  LuSettings,
  LuCheck,
  LuCopy,
  LuTrash,
  LuRotateCw,
  LuBrain,
  LuGift,
  LuSparkles,
  LuView,
  LuPlay,
  LuPin,
  LuPinOff,
  LuTouchpad,
  LuBookOpen,
  LuStickyNote,
  LuFileText,
  LuFilePlus,
  LuLockOpen,
  LuLock,
  LuDownload,
  LuX,
  LuLink,
  LuShare2,
  LuCirclePlay,
  LuPencilLine,
  LuChevronLeft,
  LuChevronRight,
  LuUserRound,
  LuFileStack,
  LuCloud,
  LuRefreshCw,
  LuBot,
} from 'react-icons/lu';

import {
  RiErrorWarningLine,
  RiDoubleQuotesL,
  RiChatHistoryLine,
  RiChatHistoryFill,
  RiUploadCloud2Line,
  RiImageAiFill,
  RiImageAiLine,
  RiExpandDiagonalLine,
  RiGuideLine,
  RiSlideshow2Line,
} from 'react-icons/ri';
import { RxEnterFullScreen, RxExitFullScreen } from 'react-icons/rx';
import { PiAtom } from 'react-icons/pi';
import { TiDocumentDelete } from 'react-icons/ti';
import { AiOutlineLoading3Quarters } from 'react-icons/ai';
import { BiText } from 'react-icons/bi';
import { BsDiscord, BsTwitterX, BsGithub, BsEnvelope } from 'react-icons/bs';
import { VscNotebookTemplate, VscFolderLibrary } from 'react-icons/vsc';

import { TfiBlackboard } from 'react-icons/tfi';

import { IoLibraryOutline } from 'react-icons/io5';
import { HiOutlineChevronDown } from 'react-icons/hi';
import { RxExit } from 'react-icons/rx';

import { AiOutlineShrink } from 'react-icons/ai';
import { TbWorldSearch, TbWorldCog, TbInputSpark } from 'react-icons/tb';
import { GrCircleQuestion } from 'react-icons/gr';
import { AiOutlineLink } from 'react-icons/ai';
import { HiOutlineTableCells } from 'react-icons/hi2';

import OpenAIIcon from '@refly-packages/ai-workspace-common/assets/openai.svg';
import ClaudeIcon from '@refly-packages/ai-workspace-common/assets/claude.svg';
import GeminiIcon from '@refly-packages/ai-workspace-common/assets/gemini.svg';
import QwenIcon from '@refly-packages/ai-workspace-common/assets/qwen.svg';
import MetaLlamaIcon from '@refly-packages/ai-workspace-common/assets/meta.svg';
import DeepSeekIcon from '@refly-packages/ai-workspace-common/assets/deepseek.svg';
import MistralIcon from '@refly-packages/ai-workspace-common/assets/mistral.svg';
import GrokIcon from '@refly-packages/ai-workspace-common/assets/grok.svg';
import ChromeIcon from '@refly-packages/ai-workspace-common/assets/chrome.svg';
import {
  MdOutlineMouse,
  MdOutlineRemoveCircleOutline,
  MdOutlinePermMedia,
  MdOutlineLightMode,
  MdOutlineDarkMode,
} from 'react-icons/md';

import ReflyLogo from '@refly-packages/ai-workspace-common/assets/logo.svg';
import { memo } from 'react';
import { IoGitNetworkOutline } from 'react-icons/io5';

export const IconReflyLogo = ReflyLogo;
export const IconCanvas = TfiBlackboard;
export const IconTemplate = VscNotebookTemplate;
export const IconAskAI = LuSparkles;
export const IconAskAIInput = TbInputSpark;
export const IconPilot = LuBot;
export const IconImage = RiImageAiLine;
export const IconImageFilled = RiImageAiFill;
export const IconDocument = LuFileText;
export const IconDocumentFilled = HiDocumentText;
export const IconCreateDocument = LuFilePlus;
export const IconResource = HiOutlineSquare3Stack3D;
export const IconResourceFilled = HiSquare3Stack3D;
export const IconImportResource = RiUploadCloud2Line;
export const IconMemo = LuStickyNote;
export const IconMemoFilled = LuStickyNote;
export const IconResponse = HiOutlineSparkles;
export const IconResponseFilled = HiSparkles;
export const IconLibrary = IoLibraryOutline;
export const IconThreadHistory = RiChatHistoryLine;
export const IconThreadHistoryFilled = RiChatHistoryFill;
export const IconPreview = LuView;
export const IconQuote = RiDoubleQuotesL;
export const IconToken = HiOutlineCircleStack;
export const IconText = BiText;
export const IconPlus = HiPlus;
export const IconMinus = HiMinus;
export const IconCheck = LuCheck;
export const IconPlay = HiMiniPlayCircle;
export const IconPlayOutline = LuCirclePlay;
export const IconRun = LuPlay;
export const IconRerun = LuRotateCw;
export const IconReply = HiOutlineReply;
export const IconMoreHorizontal = IoIosMore;
export const IconPin = LuPin;
export const IconUnpin = LuPinOff;
export const IconEdit = LuPencilLine;
export const IconDelete = LuTrash;
export const IconSearch = LuSearch;
export const IconError = RiErrorWarningLine;
export const IconLoading = AiOutlineLoading3Quarters;
export const IconSettings = LuSettings;
export const IconCopy = LuCopy;
export const IconLock = LuLock;
export const IconUnlock = LuLockOpen;
export const IconModel = LuBrain;
export const IconDown = HiOutlineChevronDown;
export const IconSubscription = LuGift;
export const IconExit = RxExit;
export const IconExpand = RiExpandDiagonalLine;
export const IconShrink = AiOutlineShrink;
export const IconWideMode = RxEnterFullScreen;
export const IconExitWideMode = RxExitFullScreen;
export const IconDocumentation = LuBookOpen;
export const IconMouse = MdOutlineMouse;
export const IconTouchpad = LuTouchpad;
export const IconThinking = PiAtom;
export const IconCodeArtifact = FiCode;
export const IconWebsite = AiOutlineLink;
export const IconTable = HiOutlineTableCells;

export const IconX = BsTwitterX;
export const IconGithub = BsGithub;
export const IconDiscord = BsDiscord;
export const IconEmail = BsEnvelope;
export const IconGuideLine = RiGuideLine;
export const IconLanguage = IoLanguage;
export const IconDeleteFile = TiDocumentDelete;
export const IconDownloadFile = LuDownload;

export const IconShare = LuShare2;
export const IconClose = LuX;
export const IconLink = LuLink;
export const IconProject = VscFolderLibrary;
export const IconLeft = LuChevronLeft;
export const IconRight = LuChevronRight;
export const IconUser = LuUserRound;
export const IconRemove = MdOutlineRemoveCircleOutline;
export const IconFiles = LuFileStack;
export const IconWorldConfig = TbWorldCog;
export const IconCloud = LuCloud;
export const IconSlideshow = RiSlideshow2Line;
export const IconRefresh = LuRefreshCw;
export const IconHome = HiOutlineHome;
export const IconMedia = MdOutlinePermMedia;
export const IconVideo = HiOutlineFilm;
export const IconAudio = HiOutlineSpeakerWave;
export const IconLightMode = MdOutlineLightMode;
export const IconDarkMode = MdOutlineDarkMode;

export const IconChrome = ChromeIcon;
export const ModelProviderIcons = {
  openai: OpenAIIcon,
  anthropic: ClaudeIcon,
  google: GeminiIcon,
  qwen: QwenIcon,
  'meta-llama': MetaLlamaIcon,
  deepseek: DeepSeekIcon,
  mistral: MistralIcon,
  xai: GrokIcon,
} as const;

const iconCache = new Map<string, string>();

export const preloadModelIcons = () => {
  for (const [provider, icon] of Object.entries(ModelProviderIcons)) {
    if (!iconCache.has(provider)) {
      iconCache.set(provider, icon);
    }
  }
};

export const getSkillIcon = (skillName: string, className?: string) => {
  switch (skillName) {
    case 'commonQnA':
      return <IconAskAI className={className} />;
    case 'webSearch':
      return <TbWorldSearch className={className} />;
    case 'generateDoc':
      return <IconDocument className={className} />;
    case 'librarySearch':
      return <IconSearch className={className} />;
    case 'recommendQuestions':
      return <GrCircleQuestion className={className} />;
    default:
      return <IconAskAI className={className} />;
  }
};

export const MemoizedIcon = memo(({ icon, className }: { icon: string; className?: string }) => (
  <img className={`w-4 h-4 ${className}`} src={icon} alt={icon} />
));

export const IconMindMap = (props: React.ComponentProps<typeof IoGitNetworkOutline>) => {
  return <IoGitNetworkOutline {...props} />;
};
