.steps-tree {
  background: var(--refly-bg-content-z2);

  .ant-tree-switcher {
    display: none !important;
  }

  // Compact the left indent since switcher is removed
  .ant-tree-indent-unit {
    width: 30px !important;
  }

  // Make the whole row clickable and full-width
  .ant-tree-node-content-wrapper {
    width: 100%;
    height: 36px;
    padding: 0 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  // Remove default background, use subtle hover and selected styles
  .ant-tree-treenode {
    background: transparent;
    width: 100%;
  }

  .ant-tree-node-content-wrapper:hover {
    background: var(--refly-tertiary-hover, rgba(0, 0, 0, 0.03));
  }

  .ant-tree-treenode-selected > .ant-tree-node-content-wrapper,
  .ant-tree-node-content-wrapper.ant-tree-node-selected {
    background: var(--refly-fill-quaternary, rgba(0, 0, 0, 0.04));
  }

  .ant-tree-title {
    flex-grow: 1;
    min-width: 0;
    overflow: hidden; // allow text truncation inside title area
  }

  // Align icon and title nicely
  .ant-tree-icon__customize {
    margin-right: 8px;
    flex-shrink: 0;
    display: flex !important;
    align-items: center;
    justify-content: center;
  }

  // Show row action buttons only on hover
  .ant-tree-node-content-wrapper:hover .steps-row-actions {
    opacity: 1 !important;
  }

  .ant-tree-node-content-wrapper {
    flex-grow: 1;
    .ant-tree-treenode-selected {
      border-radius: 8px;
      background: var(--refly-tertiary-hover);
    }
  }
}