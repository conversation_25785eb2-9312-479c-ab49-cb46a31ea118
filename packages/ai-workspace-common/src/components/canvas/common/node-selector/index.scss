.refly-base-context-selector {
  width: 260px;
  height: 300px;
  display: flex;
  flex-direction: column;

  [cmdk-root] {
    width: 100%;
    height: 100%;
    background: #fff;
    border-radius: 6px;
    font-family: var(--font-sans);
    border: 1px solid var(--gray6);
    transition: transform 100ms ease;
    outline: none;
    display: flex;
    position: relative;
    flex-direction: column;

    .dark & {
      background: rgba(22, 22, 22, 0.7);
    }
  }

  [cmdk-input-wrapper] {
    padding: 4px;
    border-bottom: 1px solid var(--gray6);
  }

  [cmdk-input] {
    font-family: var(--font-sans);
    border: none;
    width: 100%;
    font-size: 12px;
    padding-left: 4px;
    height: 32px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-color: rgba(0, 0, 0, 0.1) !important;
    border-radius: 4px;
    outline: none;
    color: var(--gray12);
    background: transparent;
    box-shadow: none !important;

    &::placeholder {
      color: var(--gray9);
    }
  }

  [cmdk-vercel-badge] {
    height: 20px;
    background: var(--grayA3);
    display: inline-flex;
    align-items: center;
    padding: 0 8px;
    font-size: 12px;
    color: var(--grayA11);
    border-radius: 4px;
    margin: 4px 0 4px 4px;
    user-select: none;
    text-transform: capitalize;
    font-weight: 500;
  }

  [cmdk-item] {
    content-visibility: auto;

    cursor: pointer;
    height: 32px;
    font-size: 12px;
    display: flex;
    gap: 8px;

    color: var(--gray11);
    user-select: none;
    will-change: background, color;
    transition: all 150ms ease;
    transition-property: none;

    &.selected {
      border-left: 2px solid #0E9F77;
      background: var(--grayA5);
    }

    &[data-selected='true'] {
      background: var(--grayA3);
      color: var(--gray12);
    }

    &[data-disabled='true'] {
      color: var(--gray8);
      cursor: not-allowed;
    }

    &:active {
      transition-property: background;
      background: var(--gray4);
    }
  }

  [cmdk-list] {
    // position: absolute;
    // z-index: 1000;
    // max-width: 750px;
    // background-color: #ffffff;
    // border-radius: 12px;
    // overflow: hidden;
    // font-family: var(--font-sans);
    // border: 1px solid var(--gray6);
    // box-shadow: var(--cmdk-shadow);
    // transition: transform 100ms ease;
    // outline: none;
    overflow-y: auto;
    height: calc(300px - 32px - 41px);
    overscroll-behavior: contain;
    transition: 100ms ease;
    transition-property: height;
  }

  [cmdk-vercel-shortcuts] {
    display: flex;
    align-items: center;
    margin-left: auto;
    gap: 8px;

    kbd {
      font-family: var(--font-sans);
      font-size: 12px;
      min-width: 20px;
      padding: 4px;
      height: 20px;
      border-radius: 4px;
      color: var(--gray11);
      background: var(--gray4);
      display: inline-flex;
      align-items: center;
      justify-content: center;
      text-transform: uppercase;
    }
  }

  [cmdk-footer] {
    height: 32px;
    width: 100%;
    position: absolute;
    background: #fff;
    bottom: 0;
    border-top: 1px solid var(--gray6);
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
    .cmdk-footer-inner {
      height: 100%;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 4px;
    }

    .cmdk-footer-hint {
      display: flex;
      align-items: center;
      font-size: 12px;

      [cmdk-vercel-shortcuts] {
        display: flex;
        align-items: center;
        gap: 8px;

        kbd {
          background: var(--gray3);
          border-radius: 3px;
          width: 12px;
          height: 12px;
          line-height: 12px;
          font-size: 12px;

          &:not(:first-child) {
            margin-left: 2px;
          }
        }
      }
    }

    .cmdk-footer-action {
      display: flex;
      flex-direction: column;
      align-items: center;

      button {
        font-size: 12px;
        height: 22px;
        border-radius: 4px;
      }
    }

    @media (prefers-color-scheme: dark) {
      background: var(--gray2);
    }
  }

  [cmdk-separator] {
    height: 1px;
    width: 100%;
    background: var(--gray5);
    margin: 4px 0;
  }

  *:not([hidden]) + [cmdk-group] {
    margin-top: 8px;
  }

  [cmdk-group-heading] {
    user-select: none;
    font-size: 12px;
    color: var(--gray11);
    padding: 0 8px;
    display: flex;
    align-items: center;
    margin-bottom: 8px;
  }

  [cmdk-empty] {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 48px;
    white-space: pre-wrap;
    color: var(--gray11);
  }
}
