.ai-copilot-message-container {
  overflow-y: auto;
  padding: 0 12px 24px;
  margin: 0 auto;
  max-width: 1024px;

  .ai-copilot-message {
    width: 100%;
    margin-top: 24px;
    display: flex;
    flex-direction: column;
  }

  .human-message-container {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;

    .human-message {
      display: flex;
      flex: 1;
      flex-direction: row;
    }

    .human-message-content {
      padding: 8px 12px;
      border-radius: 8px;
      background-color: #f1f1f0;
      background-color: #0E9F77;
      color: #fff;
      font-size: 16px;
      max-width: 100%;
      min-width: 100px;
      min-height: 40px;

      * {
        color: #fff;
      }
    }

    .context-items-container {
      margin-top: 8px;
      display: flex;
      flex-wrap: wrap;
      align-content: flex-start;
      justify-content: flex-end;
      gap: 4px;
      // overflow-y: auto;
      width: 100%;

      .context-selector-btn {
        transition: all 0.3s;

        &.active {
          border-color: rgba(0, 0, 0, 0.6);
          color: rgba(0, 0, 0, 0.8);
          background-color: #f1f1f0;
        }
      }
    }

    .context-item {
      max-width: 200px;
      border: 1px solid #e5e5e5;
      border-radius: 4px;
      cursor: pointer;
      height: 24px;
      padding: 0 4px;
      display: flex;
      flex-direction: row;
      align-items: center;
      transition: all 0.3s;

      &.limit {
        border-color: rgba(245, 63, 63, 0.3);
        background-color: rgb(255, 236, 232);
        color: rgb(245, 63, 63);
        .item-title,
        .item-type,
        .item-close,
        .item-icon {
          color: rgb(245, 63, 63);
        }
      }

      &.active {
        background-color: #f1f1f0;
        border-color: #0E9F77;
      }
      &.disabled {
        background-color: #f1f1f0;
        border-color: #e5e5e5;
        .item-title,
        .item-type,
        .item-close,
        .item-icon {
          color: rgba(0, 0, 0, 0.3);
        }
      }

      .item-content {
        height: 18px;
        display: flex;
        flex-direction: row;
        align-items: center;
        width: 100%;
        font-size: 12px;
      }

      .item-icon {
        display: flex;
        align-items: center;
        flex-shrink: 0;
        margin-right: 4px;
      }

      .item-title {
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        color: rgba(0, 0, 0, 0.6);
        min-width: 0; // 这个属性很重要，确保 flex item 可以小于其内容的最小宽度
      }

      .item-type {
        flex-shrink: 0;
        font-size: 12px;
        color: rgba(0, 0, 0, 0.8);
        font-weight: bolder;
        margin: 0 4px;
      }

      .item-close {
        flex-shrink: 0;
        font-size: 12px;
        cursor: pointer;
      }
    }

    .context-preview {
      border: 1px solid #0E9F77;
      border-radius: 8px;
      padding: 12px;
      height: 240px;
      margin-top: 4px;
      position: relative;
      background-color: #fff;
      width: 100%;

      .preview-action-container {
        position: absolute;
        right: 4px;
        top: 4px;
        z-index: 1000;

        .preview-actions {
          display: flex;
          gap: 4px;

          .preview-action-btn {
            height: 18px;
            border-radius: 4px;
            font-size: 10px;
            color: rgba(0, 0, 0, 0.6);
            border: 1px solid #e5e5e5;
            background-color: #fcfcf9;
            transition: all 0.3s;

            &:hover {
              color: rgba(0, 0, 0, 0.8);
              background-color: #f1f1f0;
            }
          }
        }
      }

      .preview-content {
        height: 100%;
        overflow-y: auto;
        width: 100%;

        p {
          margin: 0;
        }

        .arco-empty {
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }

    .message-name-and-content {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      margin-right: 12px;
      width: calc(100% - 44px);
    }

    .message-name {
      margin-bottom: 4px;
    }
  }

  .welcome-message-container {
    .welcome-message-user-container {
      display: flex;
      flex-direction: row;
      align-items: center;
    }

    .user-container-title {
      font-size: 26px;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.8);
    }

    .welcome-message-text {
      font-size: 23px;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.5);
      margin-top: 10px;
    }

    .install-skill-hint-container {
      border-radius: 8px;
      padding: 12px;
      border: 1px solid rgba(0, 0, 0, 0.1);
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      background-color: #fff;
      margin-top: 16px;

      .install-skill-hint-title {
        display: flex;
        flex-direction: column;
        align-items: center;
        color: rgba(0, 0, 0, 0.5);

        button {
          border-radius: 8px;
        }
      }
    }

    .skill-onboarding {
      margin-top: 32px;

      .manage-header {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;

        .skill-recommend-title {
          color: rgba(0, 0, 0, 0.5);
          font-size: 12px;
          font-weight: 500;
        }

        .manager-btn {
          color: rgb(var(--primary-6));
          font-size: 12px;
          border-radius: 8px;
          &:hover {
            cursor: pointer;
          }
        }
      }

      .skill-recommend-list {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        gap: 16px;
        margin-top: 4px;
      }
    }
  }

  .welcome-message-container,
  .assistant-message-container {
    .ai-copilot-related-question-container {
      margin-top: 16px;
    }

    .session-title-icon p {
      color: rgba(0, 0, 0, 0.5);
      font-weight: 500;
      font-size: 12px;
      margin-bottom: 4px;
    }

    .ai-copilot-related-question-item {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      // padding: 6px 12px;
      // max-width: 70%;

      // background: #fcfcf9;
      box-sizing: border-box;
      // border: 1px solid rgba(0, 0, 0, 0.1);

      // box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.02);
      border-radius: 8px;

      &:hover {
        color: #0E9F77;
        cursor: pointer;

        .ai-copilot-related-question-title {
          color: #0E9F77;
        }
      }

      &:not(:first-child) {
        margin-top: 4px;
      }

      .ai-copilot-related-question-title {
        font-size: 12px;
        // color: rgba(0, 0, 0, 1);
        color: #0E9F77;
      }
    }
  }

  .assistant-message-container {
    display: flex;
    flex-direction: column;

    .assistant-message {
      width: 100%;
      color: rgba(0, 0, 0, 0.9);
      display: flex;
      flex-direction: row;
      align-items: flex-start;
    }

    .assistant-message-content {
      padding: 8px 12px;
      border-radius: 8px;
      background-color: #f1f1f0;
      color: #fff;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.9);
      width: 100%;

      .message-log-collapse-container {
        margin-bottom: 8px;

        .message-log-collapse-header {
          display: flex;
          flex-direction: row;
          align-items: center;
          padding-left: 12px;

          .message-log-content {
            margin-left: 8px;
            font-size: 12px;
          }
        }

        .arco-collapse-item-content {
          padding: 0;
          padding-top: 4px;
          background-color: transparent;
        }

        .arco-collapse-item-header {
          background-color: transparent;
          padding: 0;
          background-color: #fff;
          border-radius: 8px;
          height: 32px;
          border: 0.5px solid rgba(0, 0, 0, 0.1);

          .arco-spin-icon {
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }

        .arco-collapse-item-content-box {
          padding: 0;
          background-color: transparent;
        }
      }
    }

    .message-log-container {
      margin-bottom: 8px;
    }

    .message-log-item {
      height: 32px;
      width: 100%;
      background-color: #fff;
      border-radius: 8px;
      border: 0.5px solid rgba(0, 0, 0, 0.1);
      display: flex;
      flex-direction: row;
      align-items: center;
      padding-left: 12px;

      .arco-spin-icon {
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .message-log-content {
        margin-left: 8px;
        font-size: 12px;
        color: rgba(0, 0, 0, 0.8);
        min-width: 150px;
        width: calc(100% - 60px);
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      &:not(:first-child) {
        margin-top: 4px;
      }
    }

    .message-name-and-content {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      margin-left: 12px;
      width: calc(100% - 44px);
    }

    .message-name {
      margin-bottom: 4px;
    }

    .session-related-question {
      width: 100%;
    }

    .session-source {
      width: 100%;
      margin-bottom: 16px;

      .session-title-icon p {
        color: rgba(0, 0, 0, 0.5);
        font-weight: 500;
      }
    }
  }
}

.ai-copilot-answer-action-container {
  width: 100%;
  margin-top: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .ai-copilot-answer-token-usage {
    margin-left: -4px;
  }

  .session-answer-actionbar {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;

    .session-answer-actionbar-left {
      margin-left: -4px;
      font-size: 16px;
      display: flex;
      flex-direction: row;
      align-items: center;
    }
  }

  .assist-action-item {
    color: rgba(0, 0, 0, 0.5);
    font-size: 12px;
    display: flex;
    justify-content: center;
    align-items: center;

    &:hover,
    &.active {
      cursor: pointer;
      background-color: #f1f1f0;
      color: #0E9F77;
    }
  }

  button.assist-action-item {
    position: relative;
    overflow: hidden;
    height: 24px;
    padding: 0 4px;
    border-radius: 8px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover,
    &.active {
      cursor: pointer;
      background-color: #f1f1f0;
      color: #0E9F77;
    }

    .action-text {
      opacity: 0;
      max-width: 0;
      transform: translateX(-2px);
      transition:
        opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1),
        max-width 0.4s cubic-bezier(0.4, 0, 0.2, 1),
        transform 0.4s cubic-bezier(0.4, 0, 0.2, 1),
        margin-left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      white-space: nowrap;
      display: inline-block;
    }

    &:hover .action-text {
      opacity: 1;
      max-width: 200px;
      transform: translateX(0);
      margin-left: 4px;
      transition:
        opacity 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.4s,
        max-width 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.4s,
        transform 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.4s,
        margin-left 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.4s;
    }
  }

  button {
    padding: 0 8px;
    font-size: 12px;
    background-color: transparent;

    &:hover {
      color: rgba(0, 0, 0, 1) !important;
    }
  }
}

.ai-copilot-operation-container {
  bottom: 0;
  width: 100%;
  box-shadow: 0px 4px 6px 0px rgba(16, 24, 40, 0.03);
  max-height: 100%;
  border-radius: 8px;
  overflow: hidden;

  box-sizing: border-box;

  .ai-copilot-operation-body {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
  }

  .ai-copilot-chat-container {
    position: relative;
    background-color: #fff;
    z-index: 1;
    border-radius: 8px;

    .chat-setting-container {
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      margin-top: 8px;

      .chat-input-assist-action-item {
        color: rgba(0, 0, 0, 0.5);
        font-size: 12px;
      }

      button.chat-input-assist-action-item {
        height: 24px;
        padding: 0 8px;
        border-radius: 8px;

        &:hover {
          cursor: pointer;
          background-color: #f1f1f0;
        }
      }
    }
  }

  .chat-input-container {
    // height: 125px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.1);
    border-radius: 8px;

    textarea {
      border-radius: 8px;
      background-color: transparent;
      outline: transparent;
      margin: 4px;
      margin-bottom: 0;
      box-sizing: border-box;
      width: calc(100% - 24px);
      border: none;
      resize: none;
    }
  }

  &.readonly {
    box-shadow: none;
    .chat-input-container {
      box-shadow: none;
    }
  }
}

.ai-copilot-chat-input-container {
  display: flex;
  flex-direction: column;

  .selected-skill {
    height: 32px;
    width: 100%;
    background-color: #f1f1f0;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 0 12px;

    .selected-skill-profile {
      display: flex;
      flex-direction: row;
      align-items: center;

      .selected-skill-name {
        font-weight: 600;
      }

      p {
        font-size: 12px;
        margin-left: 8px;
      }
    }

    button {
      height: 24px;
      border-radius: 8px;
    }
  }

  .ai-copilot-chat-input-body {
    display: flex;
    flex-direction: row;
  }
}
