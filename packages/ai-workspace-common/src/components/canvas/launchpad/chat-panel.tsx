import { notification, Button, Form } from 'antd';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useContextPanelStore, useContextPanelStoreShallow } from '@refly/stores';
import { useInvokeAction } from '@refly-packages/ai-workspace-common/hooks/canvas/use-invoke-action';
import { useContextFilterErrorTip } from './context-manager/hooks/use-context-filter-errror-tip';
import { genActionResultID, genUniqueId } from '@refly/utils/id';
import { useLaunchpadStoreShallow } from '@refly/stores';
import { useChatStore, useChatStoreShallow } from '@refly/stores';

import { SelectedSkillHeader } from './selected-skill-header';
import { useSkillStore, useSkillStoreShallow } from '@refly/stores';
import { ContextManager } from './context-manager';
import { ConfigManager } from './config-manager';
import { ChatActions, CustomAction } from './chat-actions';
import { ChatInput } from './chat-input';

import { useCanvasContext } from '@refly-packages/ai-workspace-common/context/canvas';
import { useSyncSelectedNodesToContext } from '@refly-packages/ai-workspace-common/hooks/canvas/use-sync-selected-nodes-to-context';
import { PiMagicWand } from 'react-icons/pi';
import { useAddNode } from '@refly-packages/ai-workspace-common/hooks/canvas/use-add-node';
import { convertContextItemsToNodeFilters } from '@refly/canvas-common';
import { IoClose } from 'react-icons/io5';
import { useUserStoreShallow } from '@refly/stores';
import { useSubscriptionStoreShallow } from '@refly/stores';
import { useUploadImage } from '@refly-packages/ai-workspace-common/hooks/use-upload-image';
import { actionEmitter } from '@refly-packages/ai-workspace-common/events/action';
import { subscriptionEnabled } from '@refly/ui-kit';
import { omit } from '@refly/utils/index';
import { cn } from '@refly/utils/cn';
import { ActionStatus, SkillTemplateConfig, ModelInfo } from '@refly/openapi-schema';
import { ContextTarget } from '@refly/common-types';
import { ProjectKnowledgeToggle } from '@refly-packages/ai-workspace-common/components/project/project-knowledge-toggle';
import { useAskProject } from '@refly-packages/ai-workspace-common/hooks/canvas/use-ask-project';
import { logEvent } from '@refly/telemetry-web';

const PremiumBanner = () => {
  const { t } = useTranslation();
  const { showPremiumBanner, setShowPremiumBanner } = useLaunchpadStoreShallow((state) => ({
    showPremiumBanner: state.showPremiumBanner,
    setShowPremiumBanner: state.setShowPremiumBanner,
  }));
  const setSubscribeModalVisible = useSubscriptionStoreShallow(
    (state) => state.setSubscribeModalVisible,
  );

  const handleUpgrade = useCallback(() => {
    logEvent('subscription::upgrade_click', 'input_banner');
    setSubscribeModalVisible(true);
  }, [setSubscribeModalVisible]);

  const handleClose = useCallback(() => {
    logEvent('subscription::input_banner_close');
    setShowPremiumBanner(false);
  }, [setShowPremiumBanner]);

  if (!showPremiumBanner) return null;

  return (
    <div className="flex items-center justify-between px-3 py-0.5 bg-gray-100 border-b dark:bg-gray-800 dark:border-gray-700">
      <div className="flex items-center justify-between gap-2 w-full">
        <span className="text-xs text-gray-600 dark:text-gray-300 flex-1 whitespace-nowrap">
          {t('copilot.premiumBanner.message')}
        </span>
        <div className="flex items-center gap-0.5">
          <Button
            type="text"
            size="small"
            className="text-xs text-green-600 px-2"
            onClick={handleUpgrade}
          >
            {t('copilot.premiumBanner.upgrade')}
          </Button>
          <Button
            type="text"
            size="small"
            icon={<IoClose size={14} className="flex items-center justify-center" />}
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-500 flex items-center justify-center w-5 h-5 min-w-0 p-0"
          />
        </div>
      </div>
    </div>
  );
};

interface ChatPanelProps {
  embeddedMode?: boolean;
  onAddMessage?: (
    message: { id: string; resultId: string; nodeId: string; data?: any },
    query?: string,
    contextItems?: any[],
  ) => void;
  onGenerateMessageIds?: () => { resultId: string; nodeId: string };
  tplConfig?: SkillTemplateConfig | null;
  onUpdateTplConfig?: (config: SkillTemplateConfig | null) => void;
  resultId?: string;
}

export const ChatPanel = ({
  embeddedMode = false,
  onAddMessage,
  onGenerateMessageIds,
  tplConfig: initialTplConfig,
  onUpdateTplConfig,
  resultId = ContextTarget.Global,
}: ChatPanelProps) => {
  const { t } = useTranslation();
  const { formErrors, setFormErrors } = useContextPanelStore((state) => ({
    formErrors: state.formErrors,
    setFormErrors: state.setFormErrors,
  }));

  // stores
  const userProfile = useUserStoreShallow((state) => state.userProfile);
  const { selectedSkill, setSelectedSkill } = useSkillStoreShallow((state) => ({
    selectedSkill: state.selectedSkill,
    setSelectedSkill: state.setSelectedSkill,
  }));
  const { contextItems, setContextItems, filterErrorInfo, runtimeConfig, setRuntimeConfig } =
    useContextPanelStoreShallow((state) => ({
      contextItems: state.contextItems,
      setContextItems: state.setContextItems,
      filterErrorInfo: state.filterErrorInfo,
      runtimeConfig: state.runtimeConfig,
      setRuntimeConfig: state.setRuntimeConfig,
    }));
  const chatStore = useChatStoreShallow((state) => ({
    newQAText: state.newQAText,
    setNewQAText: state.setNewQAText,
    selectedModel: state.selectedModel,
    setSelectedModel: state.setSelectedModel,
  }));
  const { projectId, handleProjectChange, getFinalProjectId } = useAskProject();

  // Get setActiveResultId from context panel store
  const { setActiveResultId } = useContextPanelStoreShallow((state) => ({
    setActiveResultId: state.setActiveResultId,
  }));

  // 获取选择的 MCP 服务器
  const { selectedMcpServers } = useLaunchpadStoreShallow((state) => ({
    selectedMcpServers: state.selectedMcpServers,
  }));

  const [form] = Form.useForm();
  const [currentActionResultId, setCurrentActionResultId] = useState<string | null>(null);

  // hooks
  const { canvasId, readonly } = useCanvasContext();
  const { handleFilterErrorTip } = useContextFilterErrorTip();
  const { addNode } = useAddNode();
  const { invokeAction, abortAction } = useInvokeAction({ source: 'chat-panel' });
  const { handleUploadImage, handleUploadMultipleImages } = useUploadImage();

  // Handle input focus
  const handleInputFocus = useCallback(() => {
    setActiveResultId(resultId);
  }, [resultId, setActiveResultId]);

  // automatically sync selected nodes to context
  useSyncSelectedNodesToContext();

  // Listen for action completion to clear currentActionResultId
  useEffect(() => {
    const handleActionUpdate = ({ resultId, payload }: { resultId: string; payload: any }) => {
      if (
        resultId === currentActionResultId &&
        (payload.status === 'finish' || payload.status === 'failed')
      ) {
        setCurrentActionResultId(null);
      }
    };

    actionEmitter.on('updateResult', handleActionUpdate);

    return () => {
      actionEmitter.off('updateResult', handleActionUpdate);
    };
  }, [currentActionResultId]);

  useEffect(() => {
    if (!selectedSkill?.configSchema?.items?.length) {
      form.setFieldValue('tplConfig', undefined);
    } else {
      // Create a new config object
      const newConfig: Record<string, any> = {};

      // Process each item in the schema
      for (const item of selectedSkill?.configSchema?.items || []) {
        const key = item.key;

        // Priority 0: Use external tplConfig if provided
        if (initialTplConfig && initialTplConfig[key] !== undefined) {
          newConfig[key] = initialTplConfig[key];
        }
        // Priority 1: Check if the key exists in selectedSkill.tplConfig
        else if (selectedSkill?.tplConfig && selectedSkill.tplConfig[key] !== undefined) {
          newConfig[key] = selectedSkill.tplConfig[key];
        }
        // Priority 2: Fall back to schema default value
        else if (item.defaultValue !== undefined) {
          newConfig[key] = {
            value: item.defaultValue,
            label: item.labelDict?.en ?? item.key,
            displayValue: String(item.defaultValue),
          };
        }
      }

      // Set the form value with the properly prioritized config
      form.setFieldValue('tplConfig', newConfig);
    }
  }, [selectedSkill, form, initialTplConfig]);

  const handleSendMessage = (userInput?: string) => {
    // Set active resultId when sending a message
    setActiveResultId(resultId);

    const error = handleFilterErrorTip();
    if (error) {
      return;
    }

    const { formErrors } = useContextPanelStore.getState();
    if (formErrors && Object.keys(formErrors).length > 0) {
      notification.error({
        message: t('copilot.configManager.errorTipTitle'),
        description: t('copilot.configManager.errorTip'),
      });
      return;
    }

    const tplConfig = form?.getFieldValue('tplConfig');

    // Update external tplConfig if available
    if (onUpdateTplConfig) {
      onUpdateTplConfig(tplConfig);
    }

    const { selectedSkill } = useSkillStore.getState();
    const { newQAText, selectedModel } = useChatStore.getState();
    const query = userInput || newQAText.trim();

    const { contextItems, runtimeConfig } = useContextPanelStore.getState();

    const finalProjectId = getFinalProjectId();

    // Generate new message IDs using the provided function
    const { resultId: newResultId, nodeId } = onGenerateMessageIds?.() ?? {
      resultId: genActionResultID(),
      nodeId: genUniqueId(),
    };

    // Store the current action resultId for abort functionality
    setCurrentActionResultId(newResultId);

    // Call onAddMessage callback with all required data
    if (onAddMessage) {
      onAddMessage(
        {
          id: resultId,
          resultId: newResultId,
          nodeId,
          data: {
            title: query,
            entityId: newResultId,
            metadata: {
              status: 'executing' as ActionStatus,
              contextItems: contextItems.map((item) => omit(item, ['isPreview'])),
              selectedSkill,
              selectedMcpServers,
              modelInfo: selectedModel,
              runtimeConfig,
              tplConfig,
              structuredData: {
                query,
              },
              projectId: finalProjectId,
            },
          },
        },
        query,
        contextItems,
      );
    }

    chatStore.setNewQAText('');

    // Invoke the action with the API
    invokeAction(
      {
        query,
        resultId: newResultId,
        selectedSkill: selectedSkill ?? undefined,
        modelInfo: selectedModel ?? undefined,
        contextItems,
        tplConfig,
        runtimeConfig,
        projectId: finalProjectId,
      },
      {
        entityType: 'canvas',
        entityId: canvasId,
      },
    );

    // Create node in the canvas
    const nodeFilters = [...convertContextItemsToNodeFilters(contextItems)];

    // Add node to canvas
    addNode(
      {
        type: 'skillResponse',
        data: {
          title: query,
          entityId: newResultId,
          metadata: {
            status: 'executing',
            contextItems: contextItems.map((item) => omit(item, ['isPreview'])),
            selectedMcpServers,
            selectedSkill,
            modelInfo: selectedModel,
            runtimeConfig,
            tplConfig,
            structuredData: {
              query,
            },
          },
        },
        id: nodeId,
      },
      nodeFilters,
      false,
      true,
    );
  };

  const handleAbort = () => {
    // Call abortAction without parameters to use the global currentResultId tracking
    abortAction();
    // Clear currentActionResultId to reset isExecuting state
    setCurrentActionResultId(null);
  };

  const { setRecommendQuestionsOpen, recommendQuestionsOpen } = useLaunchpadStoreShallow(
    (state) => ({
      setRecommendQuestionsOpen: state.setRecommendQuestionsOpen,
      recommendQuestionsOpen: state.recommendQuestionsOpen,
    }),
  );

  const handleRecommendQuestionsToggle = useCallback(() => {
    setRecommendQuestionsOpen(!recommendQuestionsOpen);
  }, [recommendQuestionsOpen, setRecommendQuestionsOpen]);

  const customActions: CustomAction[] = useMemo(
    () => [
      {
        icon: <PiMagicWand className="flex items-center" />,
        title: t('copilot.chatActions.recommendQuestions'),
        onClick: () => {
          handleRecommendQuestionsToggle();
        },
      },
    ],
    [handleRecommendQuestionsToggle, t],
  );

  const handleImageUpload = async (file: File) => {
    // Set active resultId when uploading an image
    setActiveResultId(resultId);

    const nodeData = await handleUploadImage(file, canvasId);
    const { contextItems: oldContextItems } = useContextPanelStore.getState();
    if (nodeData) {
      setContextItems([
        ...oldContextItems,
        {
          type: 'image',
          ...nodeData,
        },
      ]);
    }
  };

  const handleMultipleImagesUpload = async (files: File[]) => {
    // Set active resultId when uploading images
    setActiveResultId(resultId);

    const nodesData = await handleUploadMultipleImages(files, canvasId);
    if (nodesData?.length) {
      const newContextItems = nodesData.map((nodeData) => ({
        type: 'image' as const,
        ...nodeData,
      }));

      setContextItems([...contextItems, ...newContextItems]);
    }
  };

  return (
    <>
      <div className="relative w-full" data-cy="launchpad-chat-panel">
        <div
          className={cn(
            'ai-copilot-chat-container chat-input-container rounded-[7px] overflow-hidden',
            embeddedMode && 'embedded-chat-panel border !border-gray-100 dark:!border-gray-700',
          )}
        >
          <SelectedSkillHeader
            skill={selectedSkill ?? undefined}
            setSelectedSkill={setSelectedSkill}
            onClose={() => setSelectedSkill(null)}
          />
          {subscriptionEnabled && !userProfile?.subscription && <PremiumBanner />}
          <div
            className={cn(
              'px-3 bg-white dark:bg-gray-900 border-gray-100 dark:border-gray-800',
              embeddedMode && 'px-2 bg-white dark:bg-gray-900 border-gray-100 dark:border-gray-800',
            )}
          >
            <ContextManager
              className="py-2"
              contextItems={contextItems}
              setContextItems={setContextItems}
              filterErrorInfo={filterErrorInfo}
            />

            <div>
              <ChatInput
                readonly={readonly}
                query={chatStore.newQAText}
                setQuery={chatStore.setNewQAText}
                selectedSkillName={selectedSkill?.name ?? null}
                autoCompletionPlacement={'topLeft'}
                handleSendMessage={handleSendMessage}
                onUploadImage={handleImageUpload}
                onUploadMultipleImages={handleMultipleImagesUpload}
                onFocus={handleInputFocus}
              />
            </div>

            {selectedSkill?.configSchema?.items?.length ? (
              <ConfigManager
                readonly={readonly}
                key={selectedSkill?.name}
                form={form}
                formErrors={formErrors}
                setFormErrors={setFormErrors}
                tplConfig={initialTplConfig ?? undefined}
                onFormValuesChange={(_, allValues) => {
                  // Debounce form value changes to prevent cascading updates
                  const newConfig = allValues.tplConfig;
                  if (JSON.stringify(newConfig) !== JSON.stringify(initialTplConfig)) {
                    onUpdateTplConfig?.(newConfig);
                  }
                }}
                schema={selectedSkill?.configSchema}
                fieldPrefix="tplConfig"
                configScope="runtime"
                resetConfig={() => {
                  if (selectedSkill?.tplConfig) {
                    form.setFieldValue('tplConfig', selectedSkill.tplConfig);
                  } else {
                    const defaultConfig: Record<string, any> = {};
                    for (const item of selectedSkill?.configSchema?.items || []) {
                      if (item.defaultValue !== undefined) {
                        defaultConfig[item.key] = {
                          value: item.defaultValue,
                          label: item.labelDict?.en ?? item.key,
                          displayValue: String(item.defaultValue),
                        };
                      }
                    }
                    form.setFieldValue('tplConfig', defaultConfig);
                  }
                }}
              />
            ) : null}

            <ChatActions
              className="py-2"
              query={chatStore.newQAText}
              model={chatStore.selectedModel}
              setModel={(model: ModelInfo | null) => {
                if (model) {
                  chatStore.setSelectedModel(model);
                }
              }}
              runtimeConfig={runtimeConfig ?? {}}
              setRuntimeConfig={setRuntimeConfig}
              form={form}
              handleSendMessage={handleSendMessage}
              handleAbort={handleAbort}
              customActions={customActions}
              onUploadImage={handleImageUpload}
              contextItems={contextItems}
              isExecuting={!!currentActionResultId}
            />
          </div>
        </div>
      </div>
      <ProjectKnowledgeToggle
        projectSelectorClassName="max-w-[150px]"
        className="!pb-0"
        currentProjectId={projectId}
        onProjectChange={handleProjectChange}
      />
    </>
  );
};
