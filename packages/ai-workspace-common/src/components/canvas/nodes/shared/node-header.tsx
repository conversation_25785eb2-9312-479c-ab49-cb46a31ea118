import { memo, useState, useRef, useEffect, useCallback } from 'react';
import { Input, Typography } from 'antd';
import type { InputRef } from 'antd';
import cn from 'classnames';
import { CanvasNodeType, ResourceType, ResourceMeta } from '@refly/openapi-schema';
import { NodeIcon } from './node-icon';

interface NodeHeaderProps {
  fixedTitle?: string;
  title: string;
  type?: CanvasNodeType;
  resourceType?: ResourceType;
  resourceMeta?: ResourceMeta;
  canEdit?: boolean;
  source?: 'preview' | 'node';
  updateTitle?: (title: string) => void;
}

export const NodeHeader = memo(
  ({
    fixedTitle,
    title,
    type,
    resourceType,
    resourceMeta,
    canEdit = false,
    updateTitle,
    source = 'node',
  }: NodeHeaderProps) => {
    const [editTitle, setEditTitle] = useState(title);
    const [isEditing, setIsEditing] = useState(false);
    const inputRef = useRef<InputRef>(null);

    useEffect(() => {
      setEditTitle(title);
    }, [title]);

    useEffect(() => {
      if (isEditing && inputRef.current) {
        inputRef.current.focus();
      }
    }, [isEditing]);

    const handleBlur = () => {
      setIsEditing(false);
    };

    const handleChange = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
        setEditTitle(e.target.value);
        updateTitle(e.target.value);
      },
      [setEditTitle, updateTitle],
    );

    return (
      <div className={cn('flex-shrink-0', { 'mb-3': source === 'node' })}>
        <div className="flex items-center gap-2">
          <NodeIcon type={type} resourceType={resourceType} resourceMeta={resourceMeta} />
          {canEdit && isEditing ? (
            <Input
              ref={inputRef}
              className="!border-transparent font-bold focus:!bg-transparent px-0.5 py-0"
              value={editTitle}
              onBlur={handleBlur}
              onChange={handleChange}
            />
          ) : (
            <Typography.Text
              className="text-sm font-bold leading-normal truncate"
              title={title || fixedTitle}
              onClick={() => {
                if (canEdit) {
                  setIsEditing(true);
                }
              }}
            >
              {title || fixedTitle}
            </Typography.Text>
          )}
        </div>
      </div>
    );
  },
);
