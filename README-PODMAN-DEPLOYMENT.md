# Refly Podman Pod 部署指南

本指南提供了使用 Podman Pod 部署 Refly 的完整方案，所有服务运行在同一个 Pod 中，使用国内镜像源加速部署。

## 📋 前置要求

- **Podman** (版本 4.0 或更高)
- **curl** (用于健康检查)
- **足够的磁盘空间** (建议至少 10GB)
- **内存** (建议至少 4GB)

### 安装 Podman

```bash
# CentOS/RHEL/Fedora
sudo dnf install podman

# Ubuntu/Debian
sudo apt update && sudo apt install podman

# macOS
brew install podman
```

## 🏗️ 架构概览

```
┌─────────────────────────────────────────────────────────────┐
│                        Refly Pod                            │
├─────────────────────────────────────────────────────────────┤
│  前端层:    refly-web (端口: 5700)                          │
│  后端层:    refly-api (端口: 5800, 5801)                    │
├─────────────────────────────────────────────────────────────┤
│  数据存储层:                                                 │
│    • PostgreSQL (端口: 5432)                               │
│    • Redis Stack (端口: 6379, 8001)                        │
│    • MinIO (端口: 9000, 9001)                              │
│    • Qdrant (端口: 6333, 6334)                             │
├─────────────────────────────────────────────────────────────┤
│  搜索服务:  SearXNG (端口: 8080)                            │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 快速部署

### 1. 下载部署脚本

确保您已经下载了以下脚本文件：
- `deploy-refly-pod.sh` - 主部署脚本
- `verify-refly-deployment.sh` - 验证脚本
- `manage-refly-pod.sh` - 管理脚本

### 2. 设置执行权限

```bash
chmod +x deploy-refly-pod.sh
chmod +x verify-refly-deployment.sh
chmod +x manage-refly-pod.sh
```

### 3. 执行部署

```bash
./deploy-refly-pod.sh
```

部署过程包括：
1. ✅ 检查 Podman 安装
2. 📁 创建数据和配置目录
3. ⚙️ 生成环境配置文件
4. 🧹 清理现有部署
5. 🏗️ 创建 Pod
6. 🚀 按依赖顺序启动所有服务

### 4. 验证部署

```bash
./verify-refly-deployment.sh
```

或查看详细日志：

```bash
./verify-refly-deployment.sh --logs
```

## 🎯 访问服务

部署成功后，您可以访问以下服务：

| 服务 | 地址 | 说明 |
|------|------|------|
| **Refly Web 界面** | http://localhost:5700 | 主要访问入口 |
| **API 服务** | http://localhost:5800 | REST API 接口 |
| **MinIO 控制台** | http://localhost:9001 | 对象存储管理 |

### MinIO 默认凭据
- 用户名: `minioadmin`
- 密码: `minioadmin123`

## 🛠️ 管理命令

使用管理脚本进行日常操作：

```bash
# 查看状态
./manage-refly-pod.sh status

# 启动服务
./manage-refly-pod.sh start

# 停止服务
./manage-refly-pod.sh stop

# 重启服务
./manage-refly-pod.sh restart

# 查看日志
./manage-refly-pod.sh logs refly-api
./manage-refly-pod.sh logs refly-api --follow

# 进入容器
./manage-refly-pod.sh exec refly-postgres psql -U refly -d refly

# 备份数据
./manage-refly-pod.sh backup

# 更新镜像
./manage-refly-pod.sh update
```

## 📊 服务组件详情

### 核心服务

| 容器名 | 镜像 | 端口 | 说明 |
|--------|------|------|------|
| refly-web | reflyai/refly-web:latest | 80 | 前端界面 |
| refly-api | reflyai/refly-api:latest | 5800, 5801 | 后端 API |

### 数据存储

| 容器名 | 镜像 | 端口 | 数据目录 |
|--------|------|------|----------|
| refly-postgres | postgres:16-alpine | 5432 | ./refly-data/postgres |
| refly-redis | redis:7-alpine | 6379 | ./refly-data/redis |
| refly-minio | minio:latest | 9000, 9001 | ./refly-data/minio |
| refly-qdrant | qdrant:v1.13.1 | 6333, 6334 | ./refly-data/qdrant |

### 搜索服务

| 容器名 | 镜像 | 端口 | 配置目录 |
|--------|------|------|----------|
| refly-searxng | searxng:latest | 8080 | ./refly-config/searxng |

## 🔧 配置说明

### 环境变量

主要配置文件位于 `./refly-config/.env`，包含：

- **数据库配置**: PostgreSQL 连接信息
- **缓存配置**: Redis 连接信息
- **存储配置**: MinIO 对象存储配置
- **向量数据库**: Qdrant 配置
- **认证配置**: JWT 密钥等安全配置

### 数据持久化

所有数据存储在 `./refly-data/` 目录下：

```
refly-data/
├── postgres/     # PostgreSQL 数据
├── redis/        # Redis 数据
├── minio/        # MinIO 对象存储
└── qdrant/       # Qdrant 向量数据
```

## 🔍 故障排除

### 常见问题

1. **容器启动失败**
   ```bash
   # 查看容器日志
   ./manage-refly-pod.sh logs <容器名>
   
   # 重启特定容器
   podman restart <容器名>
   ```

2. **端口冲突**
   - 检查端口 5700, 5800, 5801, 9000, 9001 是否被占用
   - 修改脚本中的端口映射

3. **权限问题**
   ```bash
   # 确保数据目录权限正确
   sudo chown -R $(id -u):$(id -g) ./refly-data
   ```

4. **镜像拉取失败**
   - 检查网络连接
   - 尝试使用不同的镜像源

### 日志查看

```bash
# 查看所有容器状态
podman ps --pod

# 查看 Pod 状态
podman pod ps

# 查看特定容器日志
podman logs refly-api

# 实时跟踪日志
podman logs -f refly-api
```

## 🔄 升级指南

1. **备份数据**
   ```bash
   ./manage-refly-pod.sh backup
   ```

2. **更新镜像**
   ```bash
   ./manage-refly-pod.sh update
   ```

3. **重启服务**
   ```bash
   ./manage-refly-pod.sh restart
   ```

## 🗑️ 卸载

完全删除 Refly 部署：

```bash
# 停止并删除 Pod
./manage-refly-pod.sh remove

# 删除数据（可选，谨慎操作）
rm -rf ./refly-data ./refly-config

# 删除镜像（可选）
podman rmi reflyai/refly-api:latest reflyai/refly-web:latest
```

## 📝 注意事项

1. **生产环境使用**
   - 修改默认密码和密钥
   - 配置 HTTPS
   - 设置防火墙规则

2. **性能优化**
   - 根据负载调整容器资源限制
   - 配置适当的数据库连接池

3. **安全考虑**
   - 定期备份数据
   - 更新镜像版本
   - 监控系统资源使用

## 🆘 获取帮助

如果遇到问题，可以：

1. 查看官方文档: https://docs.refly.ai/
2. 提交 Issue: https://github.com/refly-ai/refly/issues
3. 加入社区讨论

---

**祝您使用愉快！** 🎉
