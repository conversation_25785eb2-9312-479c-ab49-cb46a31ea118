#!/bin/bash

# Refly 部署验证脚本

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

POD_NAME="refly-pod"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Pod 状态
check_pod_status() {
    log_info "检查 Pod 状态..."
    
    if ! podman pod exists "$POD_NAME"; then
        log_error "Pod '$POD_NAME' 不存在"
        return 1
    fi
    
    local pod_status=$(podman pod inspect "$POD_NAME" --format "{{.State}}")
    if [ "$pod_status" != "Running" ]; then
        log_error "Pod 状态异常: $pod_status"
        return 1
    fi
    
    log_success "Pod 状态正常"
    return 0
}

# 检查容器状态
check_containers() {
    log_info "检查容器状态..."
    
    local containers=("refly-postgres" "refly-redis" "refly-minio" "refly-qdrant" "refly-searxng" "refly-api" "refly-web")
    local failed_containers=()
    
    for container in "${containers[@]}"; do
        if podman container exists "$container"; then
            local status=$(podman inspect "$container" --format "{{.State.Status}}")
            if [ "$status" = "running" ]; then
                log_success "✓ $container: 运行中"
            else
                log_error "✗ $container: $status"
                failed_containers+=("$container")
            fi
        else
            log_error "✗ $container: 不存在"
            failed_containers+=("$container")
        fi
    done
    
    if [ ${#failed_containers[@]} -eq 0 ]; then
        log_success "所有容器状态正常"
        return 0
    else
        log_error "以下容器状态异常: ${failed_containers[*]}"
        return 1
    fi
}

# 检查服务连通性
check_service_connectivity() {
    log_info "检查服务连通性..."
    
    # 检查 PostgreSQL
    log_info "检查 PostgreSQL 连接..."
    if podman exec refly-postgres pg_isready -U refly > /dev/null 2>&1; then
        log_success "✓ PostgreSQL 连接正常"
    else
        log_error "✗ PostgreSQL 连接失败"
        return 1
    fi
    
    # 检查 Redis
    log_info "检查 Redis 连接..."
    if podman exec refly-redis redis-cli ping | grep -q "PONG"; then
        log_success "✓ Redis 连接正常"
    else
        log_error "✗ Redis 连接失败"
        return 1
    fi
    
    # 检查 MinIO
    log_info "检查 MinIO 服务..."
    if curl -s http://localhost:9000/minio/health/live > /dev/null; then
        log_success "✓ MinIO 服务正常"
    else
        log_error "✗ MinIO 服务异常"
        return 1
    fi
    
    # 检查 Qdrant
    log_info "检查 Qdrant 服务..."
    if curl -s http://localhost:6333/healthz > /dev/null; then
        log_success "✓ Qdrant 服务正常"
    else
        log_error "✗ Qdrant 服务异常"
        return 1
    fi
    
    # 检查 SearXNG
    log_info "检查 SearXNG 服务..."
    if curl -s http://localhost:8080 > /dev/null; then
        log_success "✓ SearXNG 服务正常"
    else
        log_error "✗ SearXNG 服务异常"
        return 1
    fi
    
    # 检查 API 服务
    log_info "检查 API 服务..."
    if curl -s http://localhost:5800/health > /dev/null; then
        log_success "✓ API 服务正常"
    else
        log_warning "⚠ API 服务可能还在启动中，请稍后再试"
    fi
    
    # 检查 Web 服务
    log_info "检查 Web 服务..."
    if curl -s http://localhost:5700 > /dev/null; then
        log_success "✓ Web 服务正常"
    else
        log_error "✗ Web 服务异常"
        return 1
    fi
    
    return 0
}

# 显示服务信息
show_service_info() {
    log_info "服务访问信息:"
    echo ""
    echo "🌐 Refly Web 界面:     http://localhost:5700"
    echo "🔧 API 服务:          http://localhost:5800"
    echo "📁 MinIO 控制台:      http://localhost:9001"
    echo "   用户名: minioadmin"
    echo "   密码: minioadmin123"
    echo ""
    echo "🔍 内部服务端口:"
    echo "   PostgreSQL:       localhost:5432"
    echo "   Redis:            localhost:6379"
    echo "   Qdrant:           localhost:6333"
    echo "   SearXNG:          localhost:8080"
    echo ""
}

# 显示日志
show_logs() {
    if [ "$1" = "--logs" ]; then
        log_info "显示最近的容器日志..."
        echo ""
        
        local containers=("refly-api" "refly-web" "refly-postgres" "refly-redis" "refly-minio" "refly-qdrant" "refly-searxng")
        
        for container in "${containers[@]}"; do
            if podman container exists "$container"; then
                echo "=== $container 日志 ==="
                podman logs --tail 10 "$container" 2>/dev/null || echo "无法获取 $container 日志"
                echo ""
            fi
        done
    fi
}

# 故障排除建议
troubleshooting_tips() {
    log_info "故障排除建议:"
    echo ""
    echo "1. 如果服务启动失败，请检查日志:"
    echo "   podman logs <容器名>"
    echo ""
    echo "2. 重启特定容器:"
    echo "   podman restart <容器名>"
    echo ""
    echo "3. 重启整个 Pod:"
    echo "   podman pod restart $POD_NAME"
    echo ""
    echo "4. 查看详细状态:"
    echo "   podman pod ps"
    echo "   podman ps --pod"
    echo ""
    echo "5. 如果需要完全重新部署:"
    echo "   ./deploy-refly-pod.sh"
    echo ""
}

# 主验证函数
main() {
    log_info "开始验证 Refly 部署..."
    echo ""
    
    local all_checks_passed=true
    
    # 执行各项检查
    if ! check_pod_status; then
        all_checks_passed=false
    fi
    
    echo ""
    if ! check_containers; then
        all_checks_passed=false
    fi
    
    echo ""
    if ! check_service_connectivity; then
        all_checks_passed=false
    fi
    
    echo ""
    show_service_info
    
    # 显示日志（如果请求）
    show_logs "$1"
    
    if [ "$all_checks_passed" = true ]; then
        log_success "🎉 所有检查通过！Refly 部署成功！"
        echo ""
        log_info "现在您可以访问 http://localhost:5700 开始使用 Refly"
    else
        log_error "❌ 部分检查失败，请查看上述错误信息"
        echo ""
        troubleshooting_tips
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo "Refly 部署验证脚本"
    echo ""
    echo "用法:"
    echo "  $0                验证部署状态"
    echo "  $0 --logs         验证部署状态并显示日志"
    echo "  $0 --help         显示此帮助信息"
    echo ""
}

# 处理命令行参数
case "${1:-}" in
    --help|-h)
        show_help
        exit 0
        ;;
    --logs)
        main --logs
        ;;
    "")
        main
        ;;
    *)
        echo "未知参数: $1"
        show_help
        exit 1
        ;;
esac
