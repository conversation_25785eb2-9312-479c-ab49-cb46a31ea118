#!/bin/bash

# Refly Podman Pod 部署脚本
# 使用国内镜像源加速部署

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
POD_NAME="refly-pod"
DATA_DIR="./refly-data"
CONFIG_DIR="./refly-config"

# 镜像配置 - 使用官方镜像源
POSTGRES_IMAGE="postgres:16-alpine"
REDIS_IMAGE="redis/redis-stack:latest"
MINIO_IMAGE="minio/minio:RELEASE.2025-01-20T14-49-07Z"
SEARXNG_IMAGE="searxng/searxng:latest"
QDRANT_IMAGE="reflyai/qdrant:v1.13.1"
REFLY_API_IMAGE="reflyai/refly-api:latest"
REFLY_WEB_IMAGE="reflyai/refly-web:latest"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Podman 安装
check_podman() {
    log_info "检查 Podman 安装..."
    if ! command -v podman &> /dev/null; then
        log_error "Podman 未安装，请先安装 Podman"
        exit 1
    fi
    log_success "Podman 已安装: $(podman --version)"
}

# 创建目录结构
create_directories() {
    log_info "创建数据和配置目录..."
    mkdir -p "$DATA_DIR"/{postgres,redis,minio,qdrant,searxng}
    mkdir -p "$CONFIG_DIR"
    log_success "目录创建完成"
}

# 创建环境配置文件
create_env_file() {
    log_info "创建环境配置文件..."
    cat > "$CONFIG_DIR/.env" << 'EOF'
# General Configuration
NODE_ENV=production
PORT=5800
WS_PORT=5801
ORIGIN=http://localhost:5700

# Whether automatic database schema migration is enabled
AUTO_MIGRATE_DB_SCHEMA=1

# Static Endpoint Configuration
STATIC_PUBLIC_ENDPOINT=/api/v1/misc/public
STATIC_PRIVATE_ENDPOINT=/api/v1/misc

# Redis Configuration (Pod 内使用 localhost)
REDIS_HOST=localhost
REDIS_PORT=6379

# Database Configuration (Pod 内使用 localhost)
DATABASE_URL=postgresql://refly:refly123@localhost:5432/refly?schema=refly

# Vector Store Configuration (Pod 内使用 localhost)
VECTOR_STORE_BACKEND=qdrant
QDRANT_HOST=localhost
QDRANT_PORT=6333

# MinIO Configuration (Pod 内使用 localhost)
MINIO_INTERNAL_ENDPOINT=localhost
MINIO_INTERNAL_PORT=9000
MINIO_INTERNAL_USE_SSL=false
MINIO_INTERNAL_ACCESS_KEY=minioadmin
MINIO_INTERNAL_SECRET_KEY=minioadmin123
MINIO_INTERNAL_BUCKET=refly

MINIO_EXTERNAL_ENDPOINT=localhost
MINIO_EXTERNAL_PORT=9000
MINIO_EXTERNAL_USE_SSL=false
MINIO_EXTERNAL_ACCESS_KEY=minioadmin
MINIO_EXTERNAL_SECRET_KEY=minioadmin123
MINIO_EXTERNAL_BUCKET=refly

# Authentication Configuration
AUTH_SKIP_VERIFICATION=true
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRATION_TIME=7d
JWT_REFRESH_EXPIRATION_TIME=30d

# Encryption Configuration
ENCRYPTION_KEY=0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef

# Email Authentication
EMAIL_AUTH_ENABLED=true
EMAIL_SENDER=noreply@localhost

# SearXNG Configuration (Pod 内使用 localhost)
SEARXNG_BASE_URL=http://localhost:8080

# Quota Configuration (无限制用于本地部署)
QUOTA_T1_TOKEN=-1
QUOTA_T2_TOKEN=-1
QUOTA_T1_REQUEST=-1
QUOTA_T2_REQUEST=-1
QUOTA_STORAGE_FILE=-1
QUOTA_STORAGE_OBJECT=-1
QUOTA_STORAGE_VECTOR=-1
QUOTA_FILE_PARSE_PAGE=-1
EOF
    log_success "环境配置文件创建完成"
}

# 创建 SearXNG 配置
create_searxng_config() {
    log_info "创建 SearXNG 配置..."
    mkdir -p "$CONFIG_DIR/searxng"
    cat > "$CONFIG_DIR/searxng/settings.yml" << 'EOF'
use_default_settings: true
server:
  secret_key: "your-secret-key-change-this"
  limiter: false
  image_proxy: true
search:
  safe_search: 0
  autocomplete: ""
  default_lang: "zh-CN"
ui:
  static_use_hash: true
  default_locale: "zh-CN"
  query_in_title: false
  infinite_scroll: false
  center_alignment: false
  cache_url: https://web.archive.org/web/
  search_on_category_select: true
  hotkeys: default
engines:
  - name: bing
    disabled: false
  - name: google
    disabled: false
  - name: duckduckgo
    disabled: false
EOF
    log_success "SearXNG 配置创建完成"
}

# 停止并删除现有 Pod
cleanup_existing() {
    log_info "清理现有的 Pod..."
    if podman pod exists "$POD_NAME" 2>/dev/null; then
        log_warning "发现现有的 Pod，正在停止和删除..."
        podman pod stop "$POD_NAME" 2>/dev/null || true
        podman pod rm "$POD_NAME" 2>/dev/null || true
    fi
    log_success "清理完成"
}

# 创建 Pod
create_pod() {
    log_info "创建 Refly Pod..."
    podman pod create \
        --name "$POD_NAME" \
        --publish 5700:80 \
        --publish 5800:5800 \
        --publish 5801:5801 \
        --publish 9000:9000 \
        --publish 9001:9001
    log_success "Pod 创建完成"
}

# 启动 PostgreSQL
start_postgres() {
    log_info "启动 PostgreSQL 数据库..."
    podman run -d \
        --pod "$POD_NAME" \
        --name refly-postgres \
        --volume "$PWD/$DATA_DIR/postgres:/var/lib/postgresql/data:Z" \
        --env POSTGRES_DB=refly \
        --env POSTGRES_USER=refly \
        --env POSTGRES_PASSWORD=refly123 \
        --restart always \
        "$POSTGRES_IMAGE"
    
    log_info "等待 PostgreSQL 启动..."
    sleep 10
    log_success "PostgreSQL 启动完成"
}

# 启动 Redis
start_redis() {
    log_info "启动 Redis..."
    podman run -d \
        --pod "$POD_NAME" \
        --name refly-redis \
        --volume "$PWD/$DATA_DIR/redis:/data:Z" \
        --restart always \
        "$REDIS_IMAGE" \
        redis-server --appendonly yes
    
    log_info "等待 Redis 启动..."
    sleep 5
    log_success "Redis 启动完成"
}

# 启动 MinIO
start_minio() {
    log_info "启动 MinIO 对象存储..."
    podman run -d \
        --pod "$POD_NAME" \
        --name refly-minio \
        --volume "$PWD/$DATA_DIR/minio:/data:Z" \
        --env MINIO_ROOT_USER=minioadmin \
        --env MINIO_ROOT_PASSWORD=minioadmin123 \
        --restart always \
        "$MINIO_IMAGE" \
        server /data --console-address ":9001"
    
    log_info "等待 MinIO 启动..."
    sleep 10
    log_success "MinIO 启动完成"
}

# 启动 Qdrant
start_qdrant() {
    log_info "启动 Qdrant 向量数据库..."
    podman run -d \
        --pod "$POD_NAME" \
        --name refly-qdrant \
        --volume "$PWD/$DATA_DIR/qdrant:/qdrant/storage:Z" \
        --restart always \
        "$QDRANT_IMAGE"
    
    log_info "等待 Qdrant 启动..."
    sleep 10
    log_success "Qdrant 启动完成"
}

# 启动 SearXNG
start_searxng() {
    log_info "启动 SearXNG 搜索引擎..."
    podman run -d \
        --pod "$POD_NAME" \
        --name refly-searxng \
        --volume "$PWD/$CONFIG_DIR/searxng:/etc/searxng:Z" \
        --env SEARXNG_BASE_URL=http://localhost:8080/ \
        --restart always \
        "$SEARXNG_IMAGE"
    
    log_info "等待 SearXNG 启动..."
    sleep 10
    log_success "SearXNG 启动完成"
}

# 启动 API 服务
start_api() {
    log_info "启动 Refly API 服务..."
    podman run -d \
        --pod "$POD_NAME" \
        --name refly-api \
        --env-file "$PWD/$CONFIG_DIR/.env" \
        --restart always \
        "$REFLY_API_IMAGE"
    
    log_info "等待 API 服务启动..."
    sleep 15
    log_success "API 服务启动完成"
}

# 启动 Web 服务
start_web() {
    log_info "启动 Refly Web 服务..."
    podman run -d \
        --pod "$POD_NAME" \
        --name refly-web \
        --volume "$PWD/$CONFIG_DIR/nginx-default.conf:/etc/nginx/conf.d/default.conf:Z" \
        --env API_URL=/api \
        --env COLLAB_URL=/collab \
        --env STATIC_PUBLIC_ENDPOINT=/api/v1/misc/public \
        --env STATIC_PRIVATE_ENDPOINT=/api/v1/misc \
        --restart always \
        "$REFLY_WEB_IMAGE"

    log_info "等待 Web 服务启动..."
    sleep 10

    # 重新加载 Nginx 配置以确保使用正确的配置
    podman exec refly-web nginx -s reload 2>/dev/null || true

    log_success "Web 服务启动完成"
}

# 主部署函数
main() {
    log_info "开始部署 Refly Pod..."
    
    check_podman
    create_directories
    create_env_file
    create_searxng_config
    cleanup_existing
    create_pod
    
    # 按依赖顺序启动服务
    start_postgres
    start_redis
    start_minio
    start_qdrant
    start_searxng
    start_api
    start_web
    
    log_success "Refly Pod 部署完成！"
    log_info "访问地址: http://localhost:5700"
    log_info "MinIO 控制台: http://localhost:9001 (用户名: minioadmin, 密码: minioadmin123)"
    
    # 显示 Pod 状态
    echo ""
    log_info "Pod 状态:"
    podman pod ps
    echo ""
    log_info "容器状态:"
    podman ps --pod
}

# 执行主函数
main "$@"
