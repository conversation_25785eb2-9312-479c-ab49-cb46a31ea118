---
description:
globs:
alwaysApply: true
---
# API Structure

The backend API is organized in a modular structure using NestJS. Each module contains controllers, services, and DTOs.

## Main Entry Point
- [apps/api/src/main.ts](mdc:apps/api/src/main.ts) - Application bootstrap
- [apps/api/src/app.module.ts](mdc:apps/api/src/app.module.ts) - Root module configuration

## Core Modules
- [apps/api/src/auth](mdc:apps/api/src/auth) - Authentication and authorization
- [apps/api/src/user](mdc:apps/api/src/user) - User management
- [apps/api/src/project](mdc:apps/api/src/project) - Project management
- [apps/api/src/canvas](mdc:apps/api/src/canvas) - Canvas operations
- [apps/api/src/rag](mdc:apps/api/src/rag) - Retrieval-Augmented Generation
- [apps/api/src/knowledge](mdc:apps/api/src/knowledge) - Knowledge base management
- [apps/api/src/search](mdc:apps/api/src/search) - Search functionality
- [apps/api/src/skill](mdc:apps/api/src/skill) - AI skills implementation
- [apps/api/src/share](mdc:apps/api/src/share) - Sharing functionality
- [apps/api/src/code-artifact](mdc:apps/api/src/code-artifact) - Code artifact generation

## Utilities
- [apps/api/src/utils](mdc:apps/api/src/utils) - Shared utility functions
- [apps/api/src/common](mdc:apps/api/src/common) - Common functionality

## Best Practices
- Follow NestJS module structure
- Use dependency injection
- Implement proper error handling
- Document APIs with OpenAPI