---
description:
globs:
alwaysApply: true
---
# Browser Extension Structure

The browser extension is built with WXT and follows a modular structure.

## Entry Points
- [apps/extension/src/entrypoints](mdc:apps/extension/src/entrypoints) - Different entry points for the extension
  - Background scripts
  - Content scripts
  - Popup interfaces

## Components
- [apps/extension/src/components](mdc:apps/extension/src/components) - UI components for the extension
  - Content clipper components
  - Selection components
  - Header components

## Pages
- [apps/extension/src/pages](mdc:apps/extension/src/pages) - Page components for extension UI

## State Management
- [apps/extension/src/stores](mdc:apps/extension/src/stores) - State management
- [apps/extension/src/modules](mdc:apps/extension/src/modules) - Feature modules

## Utilities
- [apps/extension/src/utils](mdc:apps/extension/src/utils) - Utility functions
- [apps/extension/src/requests](mdc:apps/extension/src/requests) - API request functions

## Internationalization
- [apps/extension/src/i18n](mdc:apps/extension/src/i18n) - Translations and i18n resources

## Best Practices
- Follow browser extension best practices
- Implement proper message passing between contexts
- Use content scripts sparingly
- Optimize for extension performance
- Be mindful of extension permissions