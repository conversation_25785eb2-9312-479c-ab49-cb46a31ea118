---
description:
globs:
alwaysApply: true
---
# Contribution Guidelines

Before contributing to Refly, please review the following guidelines from the [CONTRIBUTING.md](mdc:CONTRIBUTING.md) file.

## Setting Up the Development Environment

1. Fork and clone the repository
2. Install dependencies:
   - <PERSON><PERSON> and Docker Compose
   - Node.js v20.x (LTS)
   - PNPM package manager
3. Set up environment variables
4. Start development services

## Development Workflow

1. Find or open an issue on GitHub
2. Get approval from a team member for feature requests
3. Implement your changes following the coding standards
4. Write tests for your changes if applicable
5. Update documentation if needed
6. Submit a pull request to the `main` branch

## Code Style Guidelines

- Follow the TypeScript/JavaScript style guidelines
- Use React best practices for frontend code
- Follow NestJS patterns for backend code
- Ensure code is well-tested and documented

## Getting Help

- Join the [Discord](https://discord.gg/bWjffrb89h) community
- Open a discussion in [GitHub Discussions](https://github.com/refly-ai/refly/discussions)
- Check the [Documentation](https://docs.refly.ai)