{"compilerOptions": {"module": "commonjs", "moduleResolution": "node", "declaration": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "es2021", "sourceMap": true, "inlineSourceMap": false, "rootDir": "./src", "outDir": "./dist", "baseUrl": "./", "esModuleInterop": true, "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "inlineSources": true, "sourceRoot": "/", "composite": true, "types": ["electron", "express", "multer"], "resolveJsonModule": true}, "include": ["src/**/*"], "references": [{"path": "../api"}], "tsc-alias": {"verbose": true, "resolveFullPaths": true, "fileExtensions": {"inputGlob": "{js,jsx,mjs}", "outputCheck": ["js", "json", "jsx", "mjs"]}}}