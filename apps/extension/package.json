{"name": "@refly/extension", "description": "A free-form canvas creation platform powered by multi-threaded dialogue, knowledge integration, context memory, intelligent search and AI documents, easily transforms ideas into quality content.", "private": true, "version": "0.3.6", "type": "module", "scripts": {"predev": "wxt prepare", "dev": "wxt prepare && wxt -p 4000", "dev:firefox": "wxt prepare wxt -b firefox", "build": "wxt prepare && wxt build", "build:firefox": "wxt prepare wxt build -b firefox", "zip": "wxt prepare && wxt zip", "zip:firefox": "wxt prepare wxt zip -b firefox", "compile": "tsc --noEmit"}, "dependencies": {"@arco-design/web-react": "^2.62.1", "@arco-themes/react-refly-ai": "0.0.3", "@hey-api/client-fetch": "0.4.0", "@microsoft/fetch-event-source": "^2.0.1", "@radix-ui/react-popover": "^1.0.7", "@redux-devtools/extension": "^3.3.0", "@refly/ai-workspace-common": "workspace:*", "@refly/arco-vite-plugin-react": "1.3.3", "@refly/common-types": "workspace:*", "@refly/i18n": "workspace:*", "@refly/openapi-schema": "workspace:*", "@refly/plugin-vite-encoding": "1.3.3", "@refly/plugin-vite-watcher": "1.3.3", "@refly/stores": "workspace:*", "@refly/ui-kit": "workspace:*", "@refly/utils": "workspace:*", "@sentry/cli": "^2.31.2", "@sentry/react": "^7.113.0", "@tailwindcss/forms": "^0.5.3", "@types/is-hotkey": "~0.1.7", "@types/lodash.debounce": "~4.0.9", "@types/lodash.throttle": "^4.1.9", "@types/md5": "^2.3.2", "@types/react-syntax-highlighter": "^15.5.6", "@types/uuid": "^9.0.1", "autoprefixer": "^10.4.19", "classnames": "^2.5.1", "clsx": "^2.1.0", "copy-to-clipboard": "~3.3.3", "dayjs": "~1.11.7", "hotkeys-js": "~3.10.2", "i18next": "^25.3.2", "is-hotkey": "~0.2.0", "katex": "0.15.6", "lodash.debounce": "~4.0.8", "lodash.throttle": "~4.1.1", "md5": "^2.3.0", "p-retry": "^6.2.0", "p-timeout": "^6.1.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-draggable": "~4.4.5", "react-error-boundary": "~4.0.13", "react-i18next": "^15.6.0", "react-icons": "~5.4.0", "react-infinite-scroll-component": "~6.1.0", "react-markdown": "^8.0.7", "react-resizable-panels": "^2.0.19", "react-router-dom": "^6.22.1", "react-syntax-highlighter": "^15.5.0", "react-use": "^17.5.0", "rehype-highlight": "^6.0.0", "rehype-katex": "^6.0.2", "remark-breaks": "^3.0.2", "remark-gfm": "^3.0.1", "remark-math": "^5.1.1", "uuid": "^9.0.1", "vite-plugin-encoding": "~1.0.0", "vite-tsconfig-paths": "~4.3.2", "xpath-to-selector": "^1.1.3", "zustand": "~4.5.1", "antd": "^5.21.5", "i18next-browser-languagedetector": "^8.0.2", "@refly/errors": "workspace:*"}, "devDependencies": {"@arco-plugins/vite-react": "^1.3.3", "@refly/common-types": "workspace:*", "@types/chrome": "0.0.227", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "@vitejs/plugin-react": "^4.2.1", "code-inspector-plugin": "~0.19.2", "less": "^4.2.0", "postcss": "^8.4.21", "sass": "1.71.1", "tailwind-merge": "^2.2.2", "tailwindcss": "^3.2.6", "wxt": "workspace:*"}}