:host {
  --brand-color: #0E9F77;
  --white-color: #ffffff;
  --gray-color: #eeeeee;
  --text-color: #232946;
  font-size: 16px;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family:
    -apple-system,
    BlinkMacSystemFont,
    Segoe UI,
    Helvetica,
    Arial,
    sans-serif,
    "Apple Color Emoji",
    "Segoe UI Emoji";
}

* + * {
  box-sizing: inherit;
}

img {
  width: 1em;
  height: 1em;
  vertical-align: bottom;
}

.user-prompt {
  position: relative;
  min-width: 165px;
  max-width: 220px;
  max-height: 250px;
  display: flex;
  flex-direction: column;
  font-size: 12px;
  background: #fff;
  box-shadow: 0 8px 16px #919eab29;
  border-radius: 5px;
  z-index: 900;
  border: 1px solid #e5e8eb;

  .arco-menu-vertical .arco-menu-inner {
    padding: 0 !important;
  }

  .header {
    height: 32px;
    line-height: 32px;
    box-sizing: border-box;
    padding: 0 5px 0 8px;
    color: #637381;
    display: flex;
    font-size: 13px;
    user-select: none;
    .title {
      flex: 1;
    }

    .control {
      display: inline-flex;
      align-items: center;
      cursor: pointer;
    }
  }

  .footer {
    height: 32px;
    display: flex;
  }

  .content {
    flex: 1;
    display: flex;
    height: 100%;
    overflow-y: auto;

    .prompt-menu {
      width: 100%;
      position: relative;
      padding-bottom: 5px;
      overflow-y: overlay !important;

      .item {
        cursor: pointer;
        font-size: 13px;
        width: 100%;
        height: 32px;
        line-height: 32px;
        box-sizing: border-box;
        color: #333;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        align-items: center;
        gap: 2px;
      }

      .label-wrapper {
        flex: 1;
        cursor: pointer;
        user-select: none;
        min-width: 0;
        gap: 4px;
        display: flex;
        align-items: center;

        .label {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          word-wrap: break-word;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          flex: 1;
          min-width: 0;
        }
      }
    }
  }
}
.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.menu-item span {
  margin: 0 1em;
}

.menu-item img:first-child {
  margin-left: 0.75em;
}

.menu-item img:last-child {
  margin-right: 0.75em;
}

.selector {
  display: flex;
  justify-content: space-between;
  align-items: center;

  position: fixed;

  box-shadow: 1px 10px 100px 0 rgba(0, 0, 0, 0.1);

  border: 0.5px solid var(--gray-color);
  border-radius: 5px;

  background-color: var(--white-color);
  overflow: hidden;
}

.selector > div {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 32px;
  padding: 0 0.5em;
}

.selector .func {
  width: 80px;
  padding: 0;
  justify-content: center;
}

.selector .func span {
  margin-left: 0.5em;
  font-size: 0.875em;
}

.selector > div:hover {
  background-color: var(--gray-color);
  cursor: pointer;
}

/* 
  挂载在网页右下角的 Icon 组件样式
  */
.entry {
  display: flex;
  justify-content: space-between;
  align-items: center;

  position: fixed;
  right: 0;
  bottom: 25vh;
  height: 40px;

  padding: 0 8px;
  gap: 6px;

  box-shadow: 1px 10px 10px 0 rgba(0, 0, 0, 0.1);

  /* border: 1px solid var(--gray-color); */
  border-top-left-radius: calc(25px + 0.5em);
  border-bottom-left-radius: calc(25px + 0.5em);

  background-color: var(--white-color);

  transition: transform 200ms linear;
  transform: translateX(38px);
  border: 1px solid rgba(0, 0, 0, 0.07);
}

.entry img {
  border-radius: 50%;
}

.entry span {
  font-size: 16px;
  font-weight: bold;
  margin-right: 5px;
}

.entry.active,
.entry:hover {
  transform: translateX(1px);
  cursor: pointer;
  background-color: var(--brand-color);
}

.entry.active,
.entry:hover span {
  color: var(--white-color);
}

.main {
  position: fixed;
  top: 0;
  bottom: 0;
  right: 0;
  width: 389px;
  background-color: #ffffff;
  box-shadow: -1px 0 1px #919eab3d;
  height: 100%;
  transition: transform 200ms linear;
  transform: translateX(389px);
}

.main.active {
  transform: translateX(0);
}

#refly-app-main {
  z-index: 999999;
}

.main header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5em 0em;
  margin: 0 1em;
  border-bottom: 0.5px solid rgba(0, 0, 0, 0.16);
  height: 66px;
}

.main header .brand {
  display: flex;
  align-items: center;

  &:hover {
    cursor: pointer;
  }
}

.main header .brand img {
  width: 1.5em;
  height: 1.5em;
}

.main header .brand span {
  font-size: 1.2em;
  font-weight: bold;
  margin-left: 0.5rem;
  color: var(--text-color);
}

.main header .funcs {
  display: flex;
  align-items: center;
}

.main header .funcs img {
  transition: transform 200ms linear;
}

.main header .funcs img:hover {
  cursor: pointer;
  transform: scale(1.15);
}

.main .chat-wrapper {
  height: 100%;
  position: relative;
  overflow-y: auto;
  flex-grow: 1;
}

.main .chat-container {
  padding: 1em;
  height: 100%;
  margin-bottom: 64px;
  // overflow-y: auto;
}

.main .chat-container .example-message {
  margin-bottom: 1em;
  text-align: center;
}

.main .chat-container .example-message p {
  margin-bottom: 0.5em;
  font-size: 14px;
  font-weight: bold;
}

.main .chat-container {
  .reply-message,
  .loading-message,
  .question-message,
  .intent-message {
    display: flex;
  }
}

.main .chat-container {
  .question-message {
    justify-content: flex-end;
  }
}

.main {
  .home-content-container {
    overflow-y: auto;
    background-color: #ffffff;
    padding: 0.5em 1em;
    height: calc(100vh - 130px);

    .refly-slogan {
      font-family: PingFangSC-Medium;
      font-size: 36px;
      color: rgba(0, 0, 0, 0.8);
      margin-top: 80px;
      margin-bottom: 40px;
      line-height: 46px;
    }

    .toolbar {
      height: 44px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      button {
        color: rgba(0, 0, 0, 0.5);
      }

      button:hover {
        color: rgba(0, 0, 0, 0.8);
        background: #e8e8e2;
      }

      .content-selector-btn {
        color: rgba(0, 0, 0, 0.5) !important;
        background: transparent;
      }

      .content-selector-btn-selected {
        background: var(--brand-color);
        color: #fff !important;

        span {
          color: #fff !important;
        }
      }

      .content-selector-btn:hover {
        background: var(--brand-color);
        color: #fff !important;

        span {
          color: #fff !important;
        }
      }
    }

    .input-box {
      margin-bottom: 0.5em;
      padding: 8px;
      border-radius: 8px;
      box-sizing: border-box;
      background: #ffffff;
      border: 1px solid rgba(0, 0, 0, 0.1);
    }

    .input-box textarea {
      background-color: transparent;
      min-height: 98px;
    }

    .input-box {
      textarea:focus {
        border-color: transparent;
      }
    }

    .bar {
      display: flex;
      justify-content: flex-end;
    }

    .bar img {
      width: 18px;
      height: 18px;
      transition: transform 200ms linear;
    }

    .bar img:hover {
      transform: translateY(-2px);
      cursor: pointer;
    }

    .bar div {
      display: flex;
      align-items: center;
    }

    .bar div span {
      font-size: 0.75em;
      color: #3c3c3c;
      margin-right: 1em;
    }
  }
}

.main .footer {
  overflow-y: auto;
  background-color: #ffffff;
  padding: 0.5em 1em;
  position: fixed;
  bottom: 16px;

  .refly-slogan {
    font-family: PingFangSC-Medium;
    font-size: 36px;
    color: rgba(0, 0, 0, 0.8);
    margin-top: 80px;
    margin-bottom: 40px;
    line-height: 46px;
  }
}

.main .footer .toolbar {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  button {
    color: rgba(0, 0, 0, 0.5);
  }

  button:hover {
    color: rgba(0, 0, 0, 0.8);
    background: #e8e8e2;
  }

  .content-selector-btn {
    color: rgba(0, 0, 0, 0.5) !important;
    background: transparent;
  }

  .content-selector-btn-selected {
    background: var(--brand-color);
    color: #fff !important;

    span {
      color: #fff !important;
    }
  }

  .content-selector-btn:hover {
    background: var(--brand-color);
    color: #fff !important;

    span {
      color: #fff !important;
    }
  }
}

.main .footer .input-box {
  margin-bottom: 0.5em;
  padding: 8px;
  border-radius: 8px;
  box-sizing: border-box;
  background: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.main .footer .input-box textarea {
  background-color: transparent;
  min-height: 98px;
}

.main .footer .input-box {
  textarea:focus {
    border-color: transparent;
  }
}

.main .footer .bar {
  display: flex;
  justify-content: flex-end;
}

.main .footer .bar img {
  width: 18px;
  height: 18px;
  transition: transform 200ms linear;
}

.main .footer .bar img:hover {
  transform: translateY(-2px);
  cursor: pointer;
}

.main .footer .bar div {
  display: flex;
  align-items: center;
}

.main .footer .bar div span {
  font-size: 0.75em;
  color: #3c3c3c;
  margin-right: 1em;
}

.arco-tooltip-content {
  border-radius: 8px;
}

.footer-nav-container {
  width: 100%;
  position: fixed;
  bottom: 0;
  border-top: 0.5px solid rgba(0, 0, 0, 0.16);
  background-color: rgb(243, 243, 238);

  .footer-nav {
    width: 100%;
    padding: 0 8px;
    height: 64px;
    display: flex;
    flex-direction: row;
    color: #000;

    .nav-item {
      width: 50%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      box-sizing: border-box;

      border-top: 2px solid transparent;

      &:hover {
        cursor: pointer;
      }

      .nav-item-inner {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
      }

      .nav-item-title {
        font-size: 14px;
        font-weight: bold;
        margin-top: 8px;
      }
    }

    .nav-item.nav-item-active {
      border-top: 2px solid rgb(19, 52, 59);
    }
  }
}
