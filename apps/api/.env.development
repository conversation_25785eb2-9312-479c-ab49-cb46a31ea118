# General Configuration
NODE_ENV=development
PORT=5800
WS_PORT=5801
ORIGIN=http://localhost:5700,http://localhost:5173

# Whether automatic database schema migration is enabled
AUTO_MIGRATE_DB_SCHEMA=1

# Static Endpoint Configuration
STATIC_PUBLIC_ENDPOINT=
STATIC_PRIVATE_ENDPOINT=

# Redis Configuration
REDIS_HOST=
REDIS_PORT=36379
REDIS_USERNAME=
REDIS_PASSWORD=

# Database Configuration
DATABASE_URL=postgresql://refly:test@localhost:35432/refly?schema=refly

# Vector Store Configuration
QDRANT_HOST=
QDRANT_PORT=36333
QDRANT_API_KEY=

# Elasticsearch Configuration
ELASTICSEARCH_URL=http://localhost:39200
ELASTICSEARCH_USERNAME=
ELASTICSEARCH_PASSWORD=

# MinIO Configuration
MINIO_INTERNAL_ENDPOINT=
MINIO_INTERNAL_PORT=39000
MINIO_INTERNAL_USE_SSL=false
MINIO_INTERNAL_ACCESS_KEY=minioadmin
MINIO_INTERNAL_SECRET_KEY=minioadmin
MINIO_INTERNAL_BUCKET=refly-internal

MINIO_EXTERNAL_ENDPOINT=
MINIO_EXTERNAL_PORT=39000
MINIO_EXTERNAL_USE_SSL=false
MINIO_EXTERNAL_ACCESS_KEY=minioadmin
MINIO_EXTERNAL_SECRET_KEY=minioadmin
MINIO_EXTERNAL_BUCKET=refly-external

# Authentication Configuration
AUTH_SKIP_VERIFICATION=true
REFLY_COOKIE_DOMAIN=
REFLY_COOKIE_SECURE=
REFLY_COOKIE_SAME_SITE=
LOGIN_REDIRECT_URL=
JWT_SECRET=
JWT_EXPIRATION_TIME=
JWT_REFRESH_EXPIRATION_TIME=

# Collaboration Configuration
COLLAB_TOKEN_EXPIRY=

# Email Authentication
EMAIL_AUTH_ENABLED=true
EMAIL_SENDER=

# Resend API Key. Required if email verification is enabled.
# You can get your own key from https://resend.com/
RESEND_API_KEY=re_123

# GitHub Authentication
GITHUB_AUTH_ENABLED=false
GITHUB_CLIENT_ID=
GITHUB_CLIENT_SECRET=
GITHUB_CALLBACK_URL=

# Google Authentication
GOOGLE_AUTH_ENABLED=false
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
GOOGLE_CALLBACK_URL=

# Skill Configuration
SKILL_IDLE_TIMEOUT=60000
SKILL_EXECUTION_TIMEOUT=180000

# Stripe Configuration
STRIPE_API_KEY=
STRIPE_ACCOUNT_WEBHOOK_SECRET=test
STRIPE_ACCOUNT_TEST_WEBHOOK_SECRET=test
STRIPE_SESSION_SUCCESS_URL=
STRIPE_SESSION_CANCEL_URL=
STRIPE_PORTAL_RETURN_URL=

# Quota Configuration
QUOTA_T1_TOKEN=-1
QUOTA_T2_TOKEN=-1
QUOTA_T1_REQUEST=-1
QUOTA_T2_REQUEST=-1
QUOTA_STORAGE_FILE=-1
QUOTA_STORAGE_OBJECT=-1
QUOTA_STORAGE_VECTOR=-1
QUOTA_FILE_PARSE_PAGE=-1

# Media Generator Configuration
REPLICATE_API_KEY=