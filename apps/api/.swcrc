{"$schema": "https://json.schemastore.org/swcrc", "jsc": {"parser": {"syntax": "typescript", "tsx": false, "decorators": true, "dynamicImport": true}, "target": "es2022", "loose": false, "minify": {"compress": false, "mangle": false}, "keepClassNames": true, "transform": {"legacyDecorator": true, "decoratorMetadata": true}}, "module": {"type": "commonjs", "strict": true, "strictMode": true, "lazy": false, "noInterop": false}, "minify": false, "isModule": true}