{"name": "@refly/api", "version": "0.8.0", "description": "API server for Refly", "author": "", "private": true, "license": "ReflyAI OpenSource License", "repository": {"type": "git", "url": "git+https://github.com/refly-ai/refly.git"}, "main": "src/index.ts", "types": "src/index.ts", "scripts": {"copy-env": "ncp .env.example .env", "copy-env:develop": "ncp .env.development .env", "build": "prisma generate && tsc --build --verbose && ncp prisma/ dist/prisma/ && ncp src/generated dist/generated", "build:fast": "pnpm prisma:generate && swc src -d dist --config-file .swcrc --strip-leading-paths && ncp prisma/ dist/prisma/ && ncp src/generated dist/generated", "build:electron": "pnpm prisma:generate:sqlite && tsc --build --verbose && ncp src/generated dist/generated", "prisma:generate": "prisma format && prisma generate", "prisma:generate:sqlite": "prisma format --schema=prisma/sqlite-schema.prisma && prisma generate --schema=prisma/sqlite-schema.prisma", "gen-sqlite-schema": "ts-node src/scripts/gen-sqlite-schema.ts", "format-schema": "prisma format && prisma format --schema=prisma/sqlite-schema.prisma", "sync-db-schema": "prisma format && prisma generate && node -r ts-node/register --env-file=.env src/scripts/sync-db-schema.ts && prisma format --schema=prisma/sqlite-schema.prisma", "dev": "prisma format && prisma generate && nodemon", "dev:debug": "nodemon --inspect", "start": "node -r ./scripts/preload.js dist/main.js", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org refly-ai --project reflyd ./dist && sentry-cli sourcemaps upload --org refly-ai --project reflyd ./dist"}, "dependencies": {"@agentic/searxng": "^7.6.4", "@elastic/elasticsearch": "7.17.14", "@golevelup/nestjs-stripe": "~0.8.2", "@hocuspocus/extension-database": "~2.15.0", "@hocuspocus/extension-redis": "~2.15.0", "@hocuspocus/server": "~2.15.0", "@lancedb/lancedb": "^0.19.1", "@langchain/community": "0.3.50", "@langchain/core": "0.3.68", "@langchain/openai": "0.6.7", "@nest-lab/throttler-storage-redis": "^1.0.0", "@nestjs/bullmq": "~10.2.3", "@nestjs/common": "~10.3.9", "@nestjs/config": "~3.2.2", "@nestjs/core": "~10.3.9", "@nestjs/jwt": "~10.2.0", "@nestjs/passport": "~10.0.3", "@nestjs/platform-express": "^10.3.9", "@nestjs/platform-ws": "~10.3.9", "@nestjs/swagger": "^8.1.1", "@nestjs/throttler": "^6.3.0", "@nestjs/websockets": "~10.3.9", "@opentelemetry/api": "~1.8.0", "@opentelemetry/auto-instrumentations-node": "~0.44.0", "@opentelemetry/exporter-trace-otlp-http": "~0.50.0", "@opentelemetry/resources": "~1.24.1", "@opentelemetry/sdk-node": "~0.50.0", "@opentelemetry/semantic-conventions": "~1.24.1", "@paralleldrive/cuid2": "^2.2.2", "@prisma/client": "5.16.1", "@prisma/instrumentation": "5.16.1", "@qdrant/js-client-rest": "^1.9.0", "@refly/canvas-common": "workspace:*", "@refly/common-types": "workspace:*", "@refly/errors": "workspace:*", "@refly/openapi-schema": "workspace:*", "@refly/providers": "workspace:*", "@refly/skill-template": "workspace:*", "@refly/telemetry-node": "workspace:*", "@refly/utils": "workspace:*", "@rekog/mcp-nest": "^1.5.2", "@sentry/cli": "^2.31.2", "@sentry/node": "^7.113.0", "@sentry/profiling-node": "^7.113.0", "apache-arrow": "^20.0.0", "argon2": "^0.41.1", "avsc": "^5.7.7", "bullmq": "~5.34.4", "cheerio": "~1.0.0", "cookie-parser": "~1.4.6", "express": "~4.19.2", "get-port": "^5.1.1", "helmet": "^7.1.0", "ioredis": "^5.4.1", "json-parse-even-better-errors": "^4.0.0", "jsonrepair": "^3.12.0", "jsonwebtoken": "~9.0.2", "langchain": "0.3.30", "lodash": "^4.17.21", "lru-cache": "^10.2.0", "mime": "^3.0.0", "minio": "7.1.3", "module-alias": "~2.2.3", "ms": "^2.1.3", "nestjs-pino": "^4.0.0", "normalize-url": "^4.5.1", "object-hash": "^3.0.0", "p-limit": "3.1.0", "passport": "~0.6.0", "passport-github2": "~0.1.12", "passport-google-oauth20": "~2.0.0", "passport-jwt": "~4.0.1", "pdf-parse": "~1.1.1", "pino-http": "^10.3.0", "pino-pretty": "^11.0.0", "prisma": "5.16.1", "reading-time-estimator": "^1.11.0", "redis": "^4.6.13", "reflect-metadata": "^0.1.13", "resend": "^4.0.1", "rxjs": "^7.2.0", "sharp": "^0.33.5", "stripe": "~14.19.0", "tsconfig-paths": "~4.2.0", "uuid": "^9.0.1", "ws": "~8.17.0", "y-protocols": "^1.0.6", "yjs": "^13.6.8", "zod": "^3.25.76", "zod-to-json-schema": "3.24.6"}, "devDependencies": {"@golevelup/ts-jest": "^0.6.2", "@nestjs/testing": "~10.3.9", "@swc/cli": "^0.3.10", "@swc/core": "^1.4.8", "@types/cookie-parser": "~1.4.3", "@types/express": "^4.17.13", "@types/jest": "29.5.1", "@types/jsonwebtoken": "~9.0.6", "@types/lodash": "^4.17.4", "@types/ms": "^0.7.34", "@types/multer": "~1.4.11", "@types/object-hash": "^3.0.6", "@types/passport": "~1.0.12", "@types/passport-github2": "~1.2.5", "@types/passport-google-oauth20": "~2.0.11", "@types/passport-jwt": "~3.0.8", "@types/passport-local": "~1.0.35", "@types/pdf-parse": "^1.1.4", "@types/pg": "^8.11.4", "@types/supertest": "^2.0.11", "@types/uuid": "~9.0.0", "@types/ws": "~8.5.12", "dotenv": "~16.4.5", "jest": "29.5.0", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "29.1.0", "ts-node": "^10.9.2"}}