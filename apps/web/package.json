{"name": "@refly/web", "version": "1.0.0", "private": true, "type": "module", "scripts": {"build": "rsbuild build", "dev": "rsbuild dev", "preview": "rsbuild preview", "update:tokens": "node token/generate-css.cjs", "copy-env": "ncp .env.example .env", "copy-env:develop": "ncp .env.example .env"}, "dependencies": {"@ant-design/icons": "^5.5.2", "@monaco-editor/react": "^4.7.0", "@radix-ui/react-popover": "^1.0.7", "@redux-devtools/extension": "^3.3.0", "@refly/ai-workspace-common": "workspace:*", "@refly/common-types": "workspace:*", "@refly/errors": "workspace:*", "@refly/layout": "workspace:*", "@refly/openapi-schema": "workspace:*", "@refly/stores": "workspace:*", "@refly/telemetry-web": "workspace:*", "@refly/ui-kit": "workspace:*", "@refly/web-core": "workspace:*", "@sentry/react": "^7.113.0", "@tanstack/react-query": "^5.61.3", "@types/html2canvas": "^1.0.0", "antd": "^5.21.5", "aos": "^2.3.4", "clsx": "^2.1.0", "framer-motion": "^12.23.6", "html2canvas": "^1.4.1", "i18next": "^25.3.2", "lodash.throttle": "^4.1.1", "lucide-react": "~0.454.0", "motion": "^12.4.5", "qs": "~6.12.3", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.3.1", "react-helmet": "^6.1.0", "react-i18next": "^15.6.0", "react-icons": "~5.4.0", "react-resizable-panels": "^2.0.19", "react-router-dom": "^6.22.1", "react-tweet": "^3.2.1", "react-use": "^17.5.0", "refly-icons": "^1.0.5"}, "devDependencies": {"@rsbuild/core": "^1.4.6", "@rsbuild/plugin-react": "^1.3.4", "@rsbuild/plugin-sass": "^1.3.3", "@rsbuild/plugin-svgr": "^1.2.1", "@rsbuild/plugin-type-check": "^1.2.4", "@sentry/webpack-plugin": "^3.5.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "code-inspector-plugin": "~0.19.2", "node-polyfill-webpack-plugin": "^4.1.0", "tailwindcss": "^3", "tailwindcss-animate": "^1.0.6", "typescript": "^5.8.3"}}