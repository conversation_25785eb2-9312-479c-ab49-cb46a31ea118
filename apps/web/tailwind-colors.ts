// Auto-generated Tailwind colors configuration
// This file is generated from token files, do not edit manually

export const reflyColors = {
  'refly-Card-Border': 'var(--refly-Card-Border)',
  'refly-semi-color-border': 'var(--refly-semi-color-border)',
  'refly-semi-color-bg-0': 'var(--refly-semi-color-bg-0)',
  'refly-bg-body-z0': 'var(--refly-bg-body-z0)',
  'refly-bg-content-z2': 'var(--refly-bg-content-z2)',
  'refly-bg-control-z0': 'var(--refly-bg-control-z0)',
  'refly-bg-control-z1': 'var(--refly-bg-control-z1)',
  'refly-bg-float-z3': 'var(--refly-bg-float-z3)',
  'refly-bg-glass-content': 'var(--refly-bg-glass-content)',
  'refly-bg-canvas': 'var(--refly-bg-canvas)',
  'refly-bg-main-z1': 'var(--refly-bg-main-z1)',
  'refly-fill-active': 'var(--refly-fill-active)',
  'refly-fill-default': 'var(--refly-fill-default)',
  'refly-fill-hover': 'var(--refly-fill-hover)',
  'refly-func-danger-active': 'var(--refly-func-danger-active)',
  'refly-func-danger-default': 'var(--refly-func-danger-default)',
  'refly-func-danger-hover': 'var(--refly-func-danger-hover)',
  'refly-func-success-active': 'var(--refly-func-success-active)',
  'refly-func-success-default': 'var(--refly-func-success-default)',
  'refly-func-success-hover': 'var(--refly-func-success-hover)',
  'refly-func-warning-active': 'var(--refly-func-warning-active)',
  'refly-func-warning-default': 'var(--refly-func-warning-default)',
  'refly-func-warning-hover': 'var(--refly-func-warning-hover)',
  'refly-line': 'var(--refly-line)',
  'refly-modal-mask': 'var(--refly-modal-mask)',
  'refly-primary-active': 'var(--refly-primary-active)',
  'refly-primary-default': 'var(--refly-primary-default)',
  'refly-primary-default-last': 'var(--refly-primary-default-last)',
  'refly-primary-disabled': 'var(--refly-primary-disabled)',
  'refly-primary-hover': 'var(--refly-primary-hover)',
  'refly-Colorful-red-light': 'var(--refly-Colorful-red-light)',
  'refly-Colorful-red': 'var(--refly-Colorful-red)',
  'refly-Colorful-Blue': 'var(--refly-Colorful-Blue)',
  'refly-Colorful-orange': 'var(--refly-Colorful-orange)',
  'refly-Colorful-violet': 'var(--refly-Colorful-violet)',
  'refly-secondary-default': 'var(--refly-secondary-default)',
  'refly-secondary-hover': 'var(--refly-secondary-hover)',
  'refly-tertiary-default': 'var(--refly-tertiary-default)',
  'refly-tertiary-hover': 'var(--refly-tertiary-hover)',
  'refly-text-0': 'var(--refly-text-0)',
  'refly-text-1': 'var(--refly-text-1)',
  'refly-text-2': 'var(--refly-text-2)',
  'refly-text-3': 'var(--refly-text-3)',
  'refly-text-4': 'var(--refly-text-4)',
  'refly-text-StaticWhite': 'var(--refly-text-StaticWhite)',
} as const;
