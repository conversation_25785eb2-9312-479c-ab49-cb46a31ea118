# Feature Introduction

This section will provide a detailed introduction to the features of Refly.AI Cloud version.
Refly.AI Cloud version is a powerful visual business building platform, whose core is to help users efficiently design, implement, and manage complex functions through intuitive canvases and diverse nodes. Key features include:

*   **Canvas:** As the core working area, it supports building and orchestrating functional workflows by dragging and connecting nodes, enabling functional modularity, visual orchestration, and resource sharing and reuse.
*   **Nodes:** The basic building blocks of the canvas, representing different types of resources (such as files, tools, LLM), defining interactions and data flow through connections, and encapsulating functions and data.
*   **Knowledge Base:** Integrates resources such as documents, code, web clippings, and canvases, supports retrieval and being called by AI, providing rich context information for large models.
*   **Templates:** Allows users to save canvases as templates for easy reuse and sharing, accelerating function building in specific scenarios.
*   **Ask AI:** Integrates large model capabilities, helping users process resources and generate content through prompts, context, and knowledge base, and provides skills such as widget generation, web search, and knowledge base search.
*   **Creation Toolbar:** Provides tools for creating code components, website nodes, mind map nodes, memos, etc., enriching canvas content and functionality.
*   **Import Resources:** Supports importing web search results, uploading local files, pasting links, copying text, etc., conveniently integrating external resources into the canvas.

Through these features, Refly.AI Cloud version provides users with a flexible, efficient, and easy-to-use visual working environment, empowering users to quickly build and iterate complex business logic.