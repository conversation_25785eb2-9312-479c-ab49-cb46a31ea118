# Knowledge Base

The knowledge base is a powerful tool for centrally managing and retrieving your information. You can integrate documents, code snippets, web content, and canvases you create in Refly from different sources into the knowledge base, making it convenient for AI to retrieve and utilize.

## Core Concepts

The basic units that make up a knowledge base include:

*   **File:**
    *   Refers to document-like resources that are ultimately imported or clipped, such as uploaded documents, code files, content saved through web clipping, etc.
    *   These files are the basis for content retrieval in the knowledge base.
*   **Canvas:**
    *   Refers to the canvas projects created by users in Refly.
    *   After adding a canvas to the knowledge base, the content within the canvas can also be retrieved.

## Creating a Knowledge Base

Create a new knowledge base to organize your resources.

**Basic Information:**

*   **Name:** A unique identifier for the knowledge base, used for identification and referencing. It is recommended to use a concise and clear name.
*   **Description:** (Optional) Provides supplementary descriptions or background information about the knowledge base content to help understand its purpose.
*   **Preset Prompt:** (Optional) Sets a specific prompt for this knowledge base. This prompt will automatically take effect every time this knowledge base is called by AI, guiding the direction or content scope of the AI's response.

![Create Knowledge Base Form](/images/2025-04-26-23-33-50.webp)

**Operation Steps:**

1.  In the left navigation bar on the page, find and click **Knowledge Base**.
2.  Click the **Create Knowledge Base** button.
3.  In the pop-up window, fill in the **Name**, **Description** (optional), and **Preset System Prompt** (optional).
4.  Click the **Confirm** or **Create** button to complete the creation.

![Click Create Knowledge Base Button](/images/2025-04-26-23-34-00.webp)
![Fill in Knowledge Base Information](/images/2025-04-26-23-34-09.webp)

## Managing Knowledge Base

After creating a knowledge base, you can manage the canvases and file resources within it.

### Managing Canvases

You can search, add, delete, or remove canvases in the knowledge base.

**Main Operations:**

*   **Search:** Quickly find added canvases by name within the current knowledge base.
*   **Add:**
    *   **Add Existing Canvas:** Add other canvases you have created to the current knowledge base.
    *   **Create New Canvas:** Directly create a new canvas within the current knowledge base.
*   **Delete:** **(Please proceed with caution!)** This operation will permanently delete the canvas itself and all its content, and cannot be recovered.
*   **Remove from Knowledge Base:** Only unlinks the canvas from the current knowledge base; the canvas itself will not be deleted and will still exist in your canvas list.


#### Search Canvases
1.  Enter the target knowledge base.
2.  Switch to the **Canvas** tab.
3.  Click the **Search** icon or input box.
4.  Enter the keyword of the canvas name you want to find.
![Search Canvas Entry](/images/2025-04-26-23-34-20.webp)
![Enter Keyword to Search](/images/2025-04-26-23-34-28.webp)

#### Add Canvas
1.  Enter the target knowledge base.
2.  Switch to the **Canvas** tab.
3.  Click the **+ Add** button.
![Add Canvas Button](/images/2025-04-26-23-34-37.webp)
4.  Select **Create New Canvas** or **Add Existing Canvas**.
![Select Add Method](/images/2025-04-26-23-34-50.webp)
5.  If you select **Add Existing Canvas**, check the boxes for the canvases you want to add in the list, then click Confirm.
![Select Existing Canvas](/images/2025-04-26-23-34-59.webp)

#### Delete Canvas
1.  Enter the target knowledge base.
2.  Switch to the **Canvas** tab.
3.  Find the canvas you want to delete.
4.  Click the **"..."** (More Options) button on the right side of the canvas entry.
5.  Select **Delete Canvas** from the pop-up menu.
6.  **Confirm the deletion operation again.**
![Delete Canvas Operation](/images/2025-04-26-23-35-07.webp)

#### Remove Canvas from Knowledge Base
1.  Enter the target knowledge base.
2.  Switch to the **Canvas** tab.
3.  Check the checkbox in front of one or more canvas entries you want to remove.
![Select Canvases to Remove](/images/2025-04-26-23-35-17.webp)
4.  Click the **Delete from Knowledge Base** (or similar text remove button) above the list.
![Click Remove Button](/images/2025-04-26-23-35-28.webp)

### Managing Files

You can search, add, delete, remove, or add files to the canvas in the knowledge base.

**Main Operations:**

*   **Search:** Quickly find added files by name within the current knowledge base.
*   **Add:**
    *   **Add Existing File:** Add other files you have uploaded or processed to the current knowledge base.
    *   **Import New File:** Directly upload new files from your local machine to the current knowledge base.
*   **Delete:** **(Please proceed with caution!)** This operation will permanently delete the file itself and may not be recoverable.
*   **Remove from Knowledge Base:** Only unlinks the file from the current knowledge base; the file itself may not be deleted (depending on system implementation), but it will disappear from this knowledge base.
*   **Add to Canvas:** Quickly add the selected file(s) to a canvas associated with the current knowledge base for further processing or referencing.


#### Search Files
1.  Enter the target knowledge base.
2.  Switch to the **File** tab.
3.  Click the **Search** icon or input box.
4.  Enter the keyword of the file name you want to find.
![Search File Entry](/images/2025-04-26-23-35-36.webp)
![Enter Keyword to Search Files](/images/2025-04-26-23-35-46.webp)

#### Add File
1.  Enter the target knowledge base.
2.  Switch to the **File** tab.
3.  Click the **Add** button.
4.  Select **Add Existing File** or **Import New File**.
![Add File Operation](/images/2025-04-26-23-36-01.webp)

#### Delete File
1.  Enter the target knowledge base.
2.  Switch to the **File** tab.
3.  **Method 1 (Single Delete):** Find the file you want to delete, click the **"..."** (More Options) button on its right, and select **Delete**.
![Single File Delete](/images/2025-04-26-23-36-20.webp)
4.  **Method 2 (Batch Delete):** Check the checkboxes in front of one or more files you want to delete.
![Check Files](/images/2025-04-26-23-36-29.webp)
5.  Click the **Delete** button above the list.
6.  **Confirm the deletion operation again.**

#### Remove File from Knowledge Base
1.  Enter the target knowledge base.
2.  Switch to the **File** tab.
3.  Check the checkboxes in front of one or more files you want to remove.
4.  Click the **Remove from Knowledge Base** (or similar text remove button) above the list.
![Click Remove Button](/images/2025-04-26-23-36-38.webp)

#### Add to Canvas
1.  Enter the target knowledge base.
2.  Switch to the **File** tab.
3.  Select one or more files.
4.  Click the **Add to Canvas** button.
5.  Select the target canvas.