# 🧮 Refly Credits System Guide

This guide will help you understand Refly's credits system: what credits are used for, how to obtain them, consumption rules, package benefits, and early user retention benefits.

## 📌 What are Credits?

Refly credits are a universal billing unit that can be used to call various AI models and features on the platform. You can think of credits as the "fuel" for AI usage — each model call deducts corresponding credits based on consumption.

## 📥 How to Obtain Credits

You can obtain credits through the following methods:

1. **Subscription Packages (Recommended)**
   - Each package includes daily credit refresh + monthly bonus credits
   - Package credits are universal for all models, no modality restrictions

2. **Individual Credit Packages (Coming Soon)**
   - Pay-as-you-go, more flexible for topping up

3. **Events / Friend Invitations (Coming Soon)**
   - Receive additional bonus credits through periodic events

## 💡 Package Credit Rules

- Daily credits automatically refresh and do not accumulate to the next day
- Bonus credits are one-time distributions for advanced models or multimodal calls
- All credits can be viewed in transaction history and consumption details

## 👤 Legacy User Benefits

### ✅ Legacy Members (Plus / Pro / Max)
Original packages are retained with monthly automatic credit distribution:
- **Plus**: 700 credits/month
- **Pro**: 1300 credits/month  
- **Max**: 2000 credits/month

No manual action required, can switch to new packages anytime for more flexible benefits.

### 💎 Early Bird Unlimited Members
- Retain "Unlimited Model Usage Rights"
- Core models support unlimited calls, no credit consumption, no package switching needed

**Early bird users can use these models unlimitedly:**
1. Claude Sonnet 4
2. Claude 4 Sonnet (thinking)
3. Kimi K2
4. GPT-4o
5. GPT-4.1
6. GPT-5
7. GPT-o3
8. GPT-OSS-120B
9. Gemini 2.5 Pro
10. Gemini 2.5 Flash
11. DeepSeek V3
12. DeepSeek R1
13. Grok 4
14. Qwen3 Coder

## 📊 How to Check Credit Balance and Usage

You can check in the following locations:
- **Profile Center > Subscription** to view total credit balance and daily refresh amount
- System will estimate and show expected consumption before each model call
- Transaction history shows credit deduction details and call times

## 💰 Model Pricing

### Text Models: Token-based Billing (per 5K tokens)

| Model | Credit Consumption |
|-------|-------------------|
| Claude Sonnet 4 | ~11 credits |
| Claude Opus 4.1 | ~53 credits |
| Claude 4 Sonnet (thinking) | ~11 credits |
| Kimi K2 | ~2 credits |
| GPT-4o | ~7 credits |
| GPT-4.1 | ~6 credits |
| GPT-5 | ~7 credits |
| GPT-o3 | ~6 credits |
| GPT-OSS-120B | ~1 credit |
| Gemini 2.5 Pro | ~7 credits |
| Gemini 2.5 Flash | ~2 credits |
| DeepSeek V3 0324 | ~1 credit |
| DeepSeek R1 0528 | ~2 credits |
| Grok 4 | ~10 credits |
| Qwen3 Coder | ~3 credits |

### Image Models: Per Image Billing

| Model Name | Credit Consumption |
|------------|-------------------|
| Flux 1.1 pro ultra | ~9 credits |
| Flux 1.1 pro | ~6 credits |
| flux-dev | ~4 credits |
| Flux-Krea-Dev | ~4 credits |
| flux-schnell | ~1 credit |
| recraft-v3 | ~6 credits |
| recraft-v3-svg | ~12 credits |
| google / imagen-4 | ~6 credits |
| google / imagen-4-fast | ~3 credits |
| google / imagen-4-ultra | ~9 credits |
| doubao-Seedream-3.0-t2i | ~6 credits |
| Qwen-Image | ~4 credits |

### Video Models: Per Second Billing

| Model Name | Credit Consumption |
|------------|-------------------|
| Veo 3 Fast (Audio) | ~448 credits |
| Veo 3 | ~840 credits |
| bytedance / seedance-1-pro | ~14 credits |
| bytedance / seedance-1-lite | ~10 credits |
| kwaivgi / kling-v2.1-master | ~196 credits |
| Wan 2.2 Fast | ~7 credits |

### Audio Models: Per Use Billing

| Model Name | Credit Consumption |
|------------|-------------------|
| suno-ai/bark | ~10 credits |
| lucataco/ace-step | ~1 credit |
| haoheliu/audio-Idm | ~10 credits |
| elevenlabs-turbo-v2.5 | ~35 credits |
| elevenlabs-multilingual-v2 | ~70 credits |

## ❓ Frequently Asked Questions (FAQ)

### Q1: What are credits? How do they differ from previous memberships?

**A:** Credits are Refly's new universal billing unit for using various models, generating content, running tasks, and other activities. Unlike old memberships, credits are more flexible and no longer limited by fixed package features.

- **Before**: "Paid subscription package → Fixed feature quantity"
- **Now**: "Pay for credits → Any model and feature deducted on demand"

For example, you can use credits to:
- Call multiple models like GPT-4, Claude 3, Gemini Pro
- Run workflows like image generation/document Q&A/long image interpretation
- Upload files to knowledge base or build intelligent agents

### Q2: What about old members? Is switching to new packages mandatory?

**A:** No. We respect every legacy user's choice:
- Users with existing packages will retain their original packages and pricing
- System will distribute equivalent credits monthly for normal usage, no additional action needed
- If you want higher credits and more flexible permissions, you can upgrade to new packages anytime
- ⚠️ Once switched to new packages, you cannot switch back to old packages

### Q3: How do I know how many credits I've used? Can I control usage?

**A:** We provide detailed credit consumption records and prediction prompts:
- Shows estimated consumption before each generation (e.g., calling Kimi K2 consumes 1 credit)
- Supports credit balance queries

### Q4: Which models are supported? Are credits sufficient?

**A:** All mainstream AI models are supported with transparent and controllable usage:
- Supported models include GPT-4.1, Gemini Pro, Kimi K2, etc.
- Refly will continuously optimize model call pricing to ensure credits are more "durable"
- If credits are insufficient, you can also purchase individual credit packages anytime without upgrading packages

### Q5: I'm an early bird unlimited member, do I need to worry about credits?

**A:** Not at all. You have Refly's "unlimited green card":
- Core models permanently free with unlimited usage
- Unaffected by new system, no need to consider credit consumption
- Your permissions will be specially marked, and we'll launch a "Special Memorial Wall" in the future

### Q6: Can I try for free? Are there newcomer benefits?

**Of course!** We've prepared for every new registered user:

✅ **Free Trial Package**
- ✅ 100 daily credit refresh
- ✅ World-class model trials
- ✅ Support for uploading 100 files

Plus, there are friend invitation rewards and periodic promotional activities to easily unlock more credits! 