# 个性化设置

## 概念
个性化设置,帮助用户实现使用自己的模型以及依赖服务, 如LLM,EMB,网页Search,PDF解析,Web解析,方便用户更好的使用自由的服务使用Refly,提高安全性


### 供应商配置
#### 概念
“供应商配置”是 Refly 产品中用于连接和管理外部 AI 服务、模型或数据源（在此统称为“供应商”）的功能模块。这些“供应商”是 Refly 实现其各项高级 AI 功能（例如智能搜索、内容生成、数据处理等）的能力来源。通过配置不同的供应商，您可以为 Refly 接入特定的技术引擎或数据资源，从而定制化地增强或启用 Refly 的相关智能服务，确保它能根据您的需求高效运作。
![](/images/2025-05-13-16-54-06.png)

##### OpenAI 和类 Openai 供应商添加
以下是 OpenAI 和类 OpenAI LLM API 的配置字段说明：

| 字段 (Field)        | 类型 (Type)      | 描述 (Description)                                                                                                                               | 示例/默认值 (Example/Default Value) |
| :------------------ | :--------------- | :----------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------- |
| 类型 (Type)         | 下拉选择 (Dropdown) | 指明您正在配置的外部 AI 服务的具体类型。Refly 支持多种类型的供应商，例如 OpenAI 及其兼容的 API 服务（如 Ollama, Jina, Fireworks 等）。选择正确的类型有助于 Refly 正确地与该服务进行交互。 | OpenAI                              |
| 名称 (Name)         | 文本输入 (Text Input) | 为此供应商配置指定一个易于识别的名称。这个名称将用于在 Refly 内部引用此配置，方便您管理和区分不同的供应商。                                                               | 例如: 我的 OpenAI                   |
| 类别 (Category)     | 复选框 (Checkboxes) | 定义此供应商提供的 AI 能力。您可以选择 LLM (Large Language Model)，用于文本生成、对话等任务；或者选择 嵌入 (Embedding)，用于将文本转换为向量表示，常用于搜索和相似度匹配。一个供应商可能同时提供这两种能力。 | LLM, 嵌入                           |
| API Key             | 文本输入 (Text Input) | 这是访问供应商服务的凭证。您需要从供应商处获取您的 API 密钥，并在此处填写。请确保妥善保管您的 API 密钥，避免泄露。                                                               | `sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxx`   |
| Base URL            | 文本输入 (Text Input) | 指定供应商 API 的网络地址。对于标准的 OpenAI 服务，默认值通常是 `https://api.openai.com/v1`。如果您使用的是类 OpenAI 服务或自托管模型，您需要根据实际情况修改此 URL。 | `https://api.openai.com/v1`         |
| 是否启用 (Is Enabled) | 开关 (Toggle)    | 一个简单的开关，用于控制此供应商配置是否处于活动状态。如果禁用，Refly 将不会使用此配置。                                                                               | 启用 (On)                           |

##### 供应商介绍

###### Openroute  (有免费额度)

***概念***

Openroute 提供不同供应商多种基础的模型,提供统一的接口,同时每日提供不同供应商免费模型额度,为普通用户提供体验模型的快速通道.

***访问openroute***
* 1. 访问 http://openrouter.ai/
![](/images/2025-05-13-17-00-37.png)
* 2. 选择登录/注册账号
![](/images/2025-05-13-17-01-47.png)
![](/images/2025-05-13-17-02-31.png)
* 3. 我这里选择使用Google 进行登录(前期有Google账号,在后面使用Gemini模型也会用到)
![](/images/2025-05-13-17-04-02.png)
![](/images/2025-05-13-17-04-54.png)
* 4. 登录成功确认
![](/images/2025-05-13-17-05-32.png)


***Apikey 申请***
* 1. 点击个人图标,选择Keys
![](/images/2025-05-13-17-07-48.png)
* 2.创建API 选择Kye
![](/images/2025-05-13-17-08-59.png)
* 3. 复制Key(注意:请妥善保管你的key,避免你的财产造成损失)
![](/images/2025-05-13-17-11-48.png)
![](/images/2025-05-13-17-12-26.png)
* 4. 保存key 稍后使用key 配置 refly

***充值***
根据个人需要进行充值,充值后可以使用付费模型和提高免费模型的配额(具体请查看官网)
* 1. 进入充值页面
![](/images/2025-05-13-17-15-36.png)
* 2. 添加信用卡(注意:)
![](/images/2025-05-13-17-16-15.png)
![](/images/2025-05-13-17-17-18.png)
![](/images/2025-05-13-17-18-51.png)
* 3. 充值
![](/images/2025-05-13-17-22-48.png)
![](/images/2025-05-13-17-24-14.png)

***Refly 配置方法*** 
* 1. 填写"名称"
* 2. 将申请的key粘贴到 API Key 中
* 3. 勾选 类别,勾选启用, 点击"添加"
![](/images/2025-05-13-16-57-09.png)
###### Google (有免费额度)
***概念***

Googole  提供gemini2.5 pro  gemin-2.0-flash  等 模型具体可查看https://ai.google.dev/gemini-api/docs/models?hl=zh-cn 模型额度请参考 https://ai.google.dev/gemini-api/docs/rate-limits?hl=zh-cn 对于普通用户来讲,免费额度已经足够使用,如果需要更高用途,请参考官网套餐进行订阅
***注册或登录Google***
* 1. 访问goolge 主页(注意:由于Google不对大陆提供服务,请在除大陆以外满足Google政策的地方使用,或者使用公司提供的专有VPN进行使用)
* 2. 浏览器选择无痕模式,输入Google域名
![](/images/2025-05-13-17-37-35.png)
* 3. 注册Goolge 账号,创建账号
![](/images/2025-05-13-17-38-49.png)
![](/images/2025-05-13-17-39-42.png)
![](/images/2025-05-13-17-41-16.png)
![](/images/2025-05-13-17-41-52.png)
![](/images/2025-05-13-17-42-41.png)
![](/images/2025-05-13-17-43-12.png)
![](/images/2025-05-13-17-43-42.png)
![](/images/2025-05-13-17-44-22.png)
![](/images/2025-05-13-17-44-44.png)
![](/images/2025-05-13-17-45-11.png)
* * 大部分时候需要验证手机
![](/images/2025-05-13-17-47-05.png)
***登录Google AI Studio***
* 1. 登录 https://aistudio.google.com/prompts/new_chat 
![](/images/2025-05-13-17-49-36.png)
* 2.获取ApiKey
![](/images/2025-05-13-17-50-52.png)
或
![](/images/2025-05-13-17-51-59.png)
![](/images/2025-05-13-17-52-26.png)
![](/images/2025-05-13-17-51-29.png)

![](/images/2025-05-13-17-53-06.png)
* 3. 保存key 稍后使用key 配置 refly

***Refly 配置方法*** 
* 1. Base URL: https://generativelanguage.googleapis.com/v1beta/openai/
* 2. 其他信息参考图片
![](/images/2025-05-13-17-55-48.png)
###### DeepSeek
***注册或登录Deepseek***
* 1. 登录DeepSeek管理平台 https://platform.deepseek.com/
* 2. 注册或登录账号
![](/images/2025-05-13-18-03-21.png)
![](/images/2025-05-14-08-30-53.png)
* 3. 创建ApiKey
![](/images/2025-05-14-08-33-06.png)
![](/images/2025-05-14-08-33-30.png)
* 4. 复制ApiKey 等待后续使用
![](/images/2025-05-14-08-34-26.png)
###### Jina
***注册或登录Jina***
* 1. 打开jina https://jina.ai/
* 2. 登录或者注册jina
![](/images/2025-05-13-21-43-47.png)
![](/images/2025-05-13-21-44-18.png)
![](/images/2025-05-13-21-45-20.png)
![](/images/2025-05-13-21-46-24.png)
* 3. 复制Jina 复制Key
![](/images/2025-05-13-21-47-18.png)
***将Jina 配置到Refly***
* 1. 在供应商添加页面添加jina
![](/images/2025-05-13-21-51-18.png)
* 2.添加配置信息(Jina,可以提供Emb,重排序,Url解析服务,建议都先沟通,在模型配置根据需要进行配置),添加上面注册的key
![](/images/2025-05-13-21-53-46.png)
* 3. 添加完成
![](/images/2025-05-13-21-55-05.png)


###### Ollama
***搭建***
可以参考ollama 官网搭建教程,注意ollama 和Refly 不在同一台主机 ollama 需要配置 OLLAMA_HOST=0.0.0.0:11434 环境变量
***选择模型***
* 1. 访问Ollama 官网
![](/images/2025-05-13-22-01-24.png)
* 2. 导航到Models页面
![](/images/2025-05-13-22-01-40.png)
![](/images/2025-05-13-22-02-09.png)
* 3. 挑选需要的模型(LLM 或 EMB )
![](/images/2025-05-13-22-03-44.png)
![](/images/2025-05-13-22-04-30.png)
***运行模型***
![](/images/2025-05-13-22-04-54.png)
![](/images/2025-05-13-22-05-36.png)
![](/images/2025-05-13-22-06-24.png)
***在Refly中配置Ollama***
![](/images/2025-05-13-22-08-06.png)
![](/images/2025-05-13-22-09-17.png)
***完成Ollama 添加***
![](/images/2025-05-13-22-09-57.png)

###### Serper
***概念***
提供了Refly 搜索服务支持
***注册或登录serper***
* 1. 导航到serper网址: https://serper.dev/
* 2. 登录或者注册
![](/images/2025-05-13-22-31-07.png)
![](/images/2025-05-13-22-31-45.png)
![](/images/2025-05-13-22-32-14.png)
* 3. 生成API Key

![](/images/2025-05-13-22-32-49.png)
![](/images/2025-05-13-22-33-20.png)
***在Refly 中添加 serper***
![](/images/2025-05-13-22-34-43.png)
![](/images/2025-05-13-22-35-30.png)
![](/images/2025-05-13-22-36-21.png)


### 模型配置

#### 概念
模型配置页面涉及到三种模型  
1. LLM 大语言对话模型,LLM模型可以选择不同的供应商配置多个,然后根据业务需求选择不同的模型使用  
2.负责嵌入的EMB模型,默认配置一个
3.负责进行检索排序的 排序模型,  默认配置一个
#### LLM 模型添加
##### Openroute LLM 模型添加
* 1. 选择对应的模型供应商
* 2. 模型ID 选择对应的模型(Openroute 有大量的免费模型可以使用)
![](/images/2025-05-13-18-28-28.png)
* 3. 填写模型名称(Refly中显示的模型名称)
* 4. 上下文限制 模型最大上下文,可以参考Openroute 模型信息查看
* 5. 最大输出Tokens(可以参考Openroute 模型信息查看)
![](/images/2025-05-13-18-31-35.png)
![](/images/2025-05-13-18-32-06.png)
##### Google Gemini 2.5 PRO 模型添加
* 1.选择对应模型提供商 google
* 2.填写模型ID 可以在 https://ai.google.dev/gemini-api/docs/models?hl=zh-cn 进行查看选择,在https://ai.google.dev/gemini-api/docs/rate-limits?hl=zh-cn 查看对应模型免费额度
![](/images/2025-05-13-18-40-49.png)
![](/images/2025-05-13-18-41-41.png)
* 3. 填写模型名称(Refly中显示的模型名称)
* 4. 上下文限制 模型最大上下文, 1M
* 5. 最大输出Tokens  1M
##### 其他供应商 LLM
* 1. 基础配置信息参考上面2个 同样的方式选择供应商,填写好 模型名称(推荐和模型ID一样),模型ID(需要在提供模型的服务商平台查看),上下文和最大输出同样参考模型提供商信息.
* 2. 能力选择 函数调用,视觉(Claude3.7,gemini 等),推理(DeepSeek R1),上下文缓存
#### 嵌入模型添加
嵌入模型可以选择提供嵌入能力的供应商提供的模型,特别注意:选择嵌入模型的 "纬度"需要和重排模型一致
##### Jina 嵌入模型添加
***登录Jina网站***
* 1. 访问:  https://jina.ai/api-dashboard/embedding
![](/images/2025-05-13-22-58-09.png)
***选择模型***
* 1. 选择向量模型
![](/images/2025-05-13-22-58-59.png)
![](/images/2025-05-13-22-59-28.png)
* 2. 记录模型名称(jina-embeddings-v3)
***Refly配置嵌入模型***
* 1. 参数解释


| 字段 (Field)   | 类型 (Type)      | 描述 (Description)         | 示例/默认值 (Example/Default Value) |
| -------------- | ---------------- | -------------------------- | ---------------------------------- |
| 供应商 (Provider) | 下拉选择 (Dropdown) | 选择模型供应商             | Jina                               |
| 模型ID (Model ID) | 文本输入 (Text Input) | 模型的唯一标识符           | jina-embeddings-v3                 |
| 模型名称 (Model Name) | 文本输入 (Text Input) | 在 Refly 中显示的模型名称 | jina-embeddings-v3                 |
| 维度 (Dimension) | 数字输入 (Number Input) | 嵌入向量的维度             | 1024                               |
| 批量大小 (Batch Size) | 数字输入 (Number Input) | 处理时的批量大小           | 20                                 |
| 是否启用 (Is Enabled) | 开关 (Toggle)    | 控制模型是否启用           | 启用 (On)                          |

* 2.添加Jina 嵌入模型
![](/images/2025-05-13-23-16-34.png)
***Refly配置排序模型***
排序模型可以选择提供排序能力的供应商提供的模型,特别注意:选择排序模型的 "纬度"需要和嵌入模型一致


#### 重排模型添加
##### Jina 排序模型添加
***登录Jina网站***
* 1. 访问:  https://jina.ai/api-dashboard/reranker
![](/images/2025-05-13-23-23-51.png)
***选择模型***
* 1. 选择排序模型
![](/images/2025-05-13-23-25-28.png)
![](/images/2025-05-13-23-25-51.png)
* 2.记录模型(jina-colbert-v2)
***Refly配置排序模型***
* 1. 参数解释

| 参数中文名        | 概念解释                                                                                                | 是否必填 (根据图示) | 备注                     |
| ----------------- | ------------------------------------------------------------------------------------------------------- | ------------------- | ------------------------ |
| 供应商            | 提供该模型的组织或公司。                                                                                      | 是 (带红色星号\*)   | 通常从预设列表中选择     |
| 模型ID            | 模型在系统或供应商内部的唯一标识符，通常预定义。                                                                      | 是 (带红色星号\*)   | 通常不可编辑或由选择决定 |
| 模型名称          | 用户为该模型实例设定的可读名称，方便管理和区分。                                                                     | 是 (带红色星号\*)   | 用户可自定义             |
| 返回结果数量      | 模型进行查询时返回的最大结果条目数。                                                                                | 否                  | 根据需求设置             |
| 相关性阈值        | 用于过滤模型结果的相关性得分下限，只有高于此阈值的结果才会被返回。                                                              | 否                  | 根据需求设置             |
| 是否启用          | 控制该模型配置当前是否生效的开关。                                                                                    | -                   | 开关控件，默认为“开”     |

* 2. 添加排序模型
![](/images/2025-05-13-23-34-53.png)
![](/images/2025-05-13-23-35-15.png)


### 解析配置
#### 默认内置
![](/images/2025-05-13-23-37-13.png)
##### 选择不同供应商
![](/images/2025-05-13-23-38-18.png)
![](/images/2025-05-13-23-38-37.png)
![](/images/2025-05-13-23-38-56.png)


### 默认模型
![](/images/2025-05-13-23-40-29.png)