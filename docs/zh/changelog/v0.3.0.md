# v0.3.0 更新日志

## 🦹 总结

🎉 Refly重磅更新！全面升级知识库文件处理能力（支持7+主流格式）和AI多模态交互（支持多图片输入），新增DeepSeek R1思维链展示、画布编辑增强（支持撤销/重做）等重要功能，让您的知识管理和创作体验更加流畅高效！

## **🌟** 新功能支持

- **📚 知识库正式支持文件上传**
  - 涵盖主流 7+ 格式，包括 PDF, DOCX, RTF, TXT, MD, HTML, EPUB 等
  - 支持展示基于文件扩展名的图标，提升辨识效果
  - 支持下载文件
- **🖼️ AI 提问支持多模态输入**
  - 包括支持多张图片上传提问（拖拽到输入框、点击上传、直接拖拽到画布等方式）、组合图片和知识库知识进行提问，包括 JPG, PNG, JPEG, WEBP,  GIF, TIFF, BMP 等图片格式
  - 支持在整个提问历史上消费提问历史的图片
  - 支持全屏预览图片并支持翻转、放大/缩小、下载等常规操作
- **🤖 AI 能力更新**
  - 支持 DeepSeek R1 展示思维链思考过程
  - 支持其他模型可以基于 DeepSeek R1 的思维链过程提问
  - 支持 OpenAI o3-mini 模型
  - 优化 AI 联网搜索/知识库搜索在 DeepSeek R1 下的过慢的问题
- **🔌 浏览器插件能力更新**
  - 支持剪藏网页时，解析网页内容的准确率大幅度提升，绝大部分网页都支持解析成功
  - 支持在剪藏时可以修改 URL 和 Title，核心服务于复制文本剪藏使用 
- **🎨 画布基础能力更新**
  - 支持撤销/重做（Undo/Redo）能力，支持按键或者快捷键进行操作
  - Memo 节点支持常用调色盘，支持 10+ 常用颜色设置，让画布更加好看
  - 优化删除节点等操作的弹窗通知展示，超出省略展示
  - 支持 AI 节点可以编辑标题和展示完整提问提示词
  - 支持在画布名称未设置时，自动设置画布名称，或者支持 AI 自动生成画布名称
- **🚀 资源和 AI 回答节点展示优化**
  - 支持渲染公式
  - 支持渲染 Mermaid 可视化流程图
  - 支持资源导入时将其中的图片自动转存到 Refly，避免因第三方 Referrer 设置导致无法查看图片的问题
  - 支持编辑节点名称，增加辨识度
- **📝 知识库基础操作能力更新**
  - 支持内容为空时的默认操作，添加资源/创建文档

## **💫** 核心问题优化

- 🔧 修复节点因拉升导致紧凑模式失效的问题
- 🔍 修复 AI 节点渲染代码块时点击 「复制」代码时会把「Copy/复制」文案添加到剪切板的问题
- 🖱️ 拖动时隐藏节点菜单
- ⚙️ 在重新运行时，修复上下文和模型配置丢失的问题