# v0.5.0 更新日志

## 🚀 总结

**Refly 重磅更新：新增 2+ 开源贡献者、10+ 实用模板，全面升级知识库分区管理、线性对话、思维导图和文档大纲功能，同时优化画布性能支持数十节点流畅使用，打造更强大的 AI 原生创作平台！**

## 👨‍💻 新增开源贡献者

本次我们新增 2+ 新贡献者，感谢他们对 Refly 做出的贡献 🎉 🎉 🎉

- **Anthhub** [GitHub](https://github.com/anthhub)
    - [PR #681](https://github.com/refly-ai/refly/pull/681)
    - [PR #681](https://github.com/refly-ai/refly/pull/681)
    - [PR #676](https://github.com/refly-ai/refly/pull/676)
- **pzc163** [GitHub](https://github.com/pzc163)
    - [PR #682](https://github.com/refly-ai/refly/pull/682)

## 📑 新增模板

本次我们新增 10+ 新模板，涵盖培训课件、脑图生成文稿、经营会主题阅读、拆书、Mermaid 功能特性研究、自媒体封面配图、本地部署手册编写、菜谱提示词等

- **培训课件**：[查看模板](https://refly.ai/preview/canvas/can-jblcltgp6stbkp1x1u73ipqv)
- **游戏编程挑战**：[查看模板](https://refly.ai/preview/canvas/can-d1qabd0uyuc4mukirivsdsny)
- **脑图生成文稿**：[查看模板](https://refly.ai/preview/canvas/can-ewdi2tzzk0r7dqwub5hkbv0c)
- **经营会主题拆书**：[查看模板](https://refly.ai/preview/canvas/can-pkbqimy0jbkuo0tfolwczow9)
- **自媒体封面**：[查看模板](https://refly.ai/share/canvas/can-hcglpaf9e79rvxntp62zb4m5)
- **菜谱提示**：[查看模板](https://refly.ai/share/canvas/can-rd5jivtn9q23uxjw3sx4u0on)
- **技术培训课件**：[查看模板](https://refly.ai/share/canvas/can-rb0sotb7q4rhkbln6d9tsqj7)
- **部署手册编写**：[查看模板](https://refly.ai/share/canvas/can-g04drhb8tr5kp0snimvb3x7m)
- **Mermaid 功能特性研究**：[查看模板](https://refly.ai/share/canvas/can-pzeppylav0jgmu71zncfdnzb)
- **投资调研可视化**：[查看模板](https://refly.ai/preview/canvas/can-dxwtqdkjbj5mdildpqqewc7m)

## 🌟 新功能支持

- **📚 新增知识库分区（类 Claude Project 能力）**
    - 支持创建多个知识库，然后对不同的知识、画布进行分区管理
    - 每个知识库支持设置自定义提示词，可以让不同知识库享受不同的定制体验
    - 支持每个 AI 提问时可以选择知识库挂载，极大增强 AI 回答的效果和关联性
- **💬 全新线性对话支持，完美适配线性会话和画布多线程对话**
    - 支持全局级别的线性对话，可以类似 ChatGPT 一样持续追问并保持上下文
    - 支持对任意 AI 回答卡片进行线性对话持续追问
    - 支持上下文添加到选中的线性会话面板
- **🧠 全新思维导图能力支持**
    - 现在可以支持 AI 组合上下文生成类 NotebookLM 级别的思维导图
    - 思维导图支持新增/删除节点，每个节点支持 Markdown 级别的操作
    - 思维导图支持一键分享发布传播
    - 尤其适合拆书、知识点梳理等场景
- **📋 全新文档大纲能力**
    - 支持文档展示目录大纲
- **📊 支持全新的 mermaid 渲染**
    - 支持看板、饼图、实体关系图等 22 种图渲染
- **🔍 全新的预览面板拖动和宽屏能力**
    - 支持预览面板可以展示宽屏模式
    - 支持预览面板可以拖拽调整位置
- **🎬 新增文档中视频教程展示**
    - 展示目前社区录制的 10+ 视频教程 [查看教程](https://docs.refly.ai/zh/guide/video-tutorials)
- **✏️ 节点编辑增强**
    - 支持所有节点可以编辑标题，方便做节点区分展示
    - 支持画布节点的对齐线和网格吸附能力，极大优化画布排版体验

## 💫 核心问题优化

- 🛠️ 修复画布复制时 AI 回答次数异常处理问题
- 🐳 支持 Docker 本地部署可以兼容端口冲突，修复本地部署不能显示登录入口的问题
- ⏱️ 优化 AI 回答超时问题，修复因为长回答（耗时较久）导致的内容闪屏覆盖或者报错问题
- 🔄 全新回答错误细化展示能力，针对模型调用出错、超时、请求速率限制进行展示
- 📋 修复节点复制内容时只有部分内容的问题
- 📍 优化图片、资源、文档等添加时的位置问题，默认加到视口处
- 🚀 进一步优化大画布下的性能，目前可以支持几十+节点流畅使用