# v0.4.2 更新日志

## 🎯 概述

🚀 Refly v0.4.2 重磅发布！本次更新带来多项重要功能升级和体验优化：

- 📚 **画布模板系统**：推出完整的画布模板和案例库系统
- 📊 **文档表格支持**：全新的表格编辑和渲染能力
- 🔗 **分享功能增强**：一键分享各类内容的完整解决方案
- 🖼️ **预览体验升级**：全新的图片和卡片预览系统
- 🎨 **画布交互优化**：更灵活的节点管理和展示方式
- ⚡️ **性能全面提升**：从核心到界面的全方位优化

本次更新致力于提供更专业、更流畅的创作体验，让您的工作更加高效。

## 🌟 新功能

### 画布模板与案例库
- **📚 正式支持画布模板和案例库！**
  - 支持近 30+ 画布模板案例
  - 在新建画布时自动推荐画布模板，减少上手门槛
  - 支持画布可以发布为模板
  - 支持基于模板库模板创建画布
  - 支持复制我已经创建的画布

### 文档增强
- **📊 正式支持文档表格**
  - 支持添加表格，表格支持操作增删列、行
  - 支持 AI 回答表格插入文档渲染表格
  - 支持复制 Markdown 粘贴插入文档渲染表格

### 分享与展示
- **🔗 分享能力增强**
  - 支持一键分享回答、代码组件和文档，并生成可分享链接

### 预览体验
- **🖼️ 图片和卡片预览大更新**
  - 支持图片节点、AI 回答、资源、文档、SVG/Mermaid 等便捷支持图片全屏预览
  - 支持图片可以缩放操作
  - 支持在关闭卡片预览之后，节点仍然可以全屏预览
  - 支持右键快捷全屏预览 AI 回答、代码节点、网站节点、图片等

### 展示优化
- **🎨 画布交互增强**
  - 支持画布节点分组可以修改名称和设置背景颜色，修复分组拖拽连线对齐问题
  - 支持网站节点最大宽度调整，方便做画布展示

### 落地页支持
- **🌐 支持落地页案例**
  - 支持画布案例页面 https://refly.ai/use-cases-gallery
  - 支持生成产物案例页面 https://refly.ai/artifact-gallery
  - 支持 Github README 展示案例

## 💫 核心优化

### 性能提升
- **⚡️ 系统性能**
  - 优化 AI 回答不稳定问题，包括网页爬取过大导致报错、输出过长导致报错等
  - 极大优化代码组件编辑器的加载性能
  - 优化代码块字体为等宽字体
  - 极大优化画布性能，支持用户可以设置折叠节点模式做动态性能优化

### 问题修复
- **🛠️ 关键修复**
  - 修复部分未登录或鉴权失败的情况下无限刷新的问题
  - 修复代码组件节点resize后无法交互的问题
  - 修复代码组件过大（> 1500 行）等无法发布分享的问题
  - 修复复制画布的若干问题
  - 修复调用免费模型计次的问题
  - 修复在 AI 回答时渲染 react 代码导致报错的问题

### 体验优化
- **✨ 细节提升**
  - 支持成本优化，支持 Claude Context Cache 能力
  - 优化基础问答，默认不做可视化 Instructions 指示
  - 优化 SVG 等下载之后宽高比不一致问题，修复 SVG 多个样式互相影响问题
  - 修复基于 AI 回答节点创建文档不全的问题
  - 优化折叠模式之后，新节点跟随全局折叠状态，切可折叠 reasoning 过程

## 📢 其他更新

- 🔄 正式恢复定价