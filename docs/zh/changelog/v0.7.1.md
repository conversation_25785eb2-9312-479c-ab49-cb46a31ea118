# v0.7.1 更新日志

## 🌈 版本亮点

本次更新隆重推出 MCP Store，您只需配置 API Key 即可轻松接入 GitHub、Zapier 等五大平台。更棒的是，技能调用现已支持后台执行，关闭浏览器任务也能继续运行。此外，新版本还修复了多项问题，优化了系统稳定与使用体验。

## 🌟 新功能支持

- ⚙️ 新增 MCP Store，支持快捷添加 MCP Server
  - 首发 5 款 MCP Server：GitHub、Firecrawl、DeepWiki、Gitee、Zapier
  - 仅需配置对应平台的 API Key 即可快速接入并使用
- 技能调用支持后台执行
  - 现在您可以随时关闭浏览器，技能调用仍然会在后台继续运行，随时返回查看运行结果

## 🛠️ 系统优化与问题修复

🚀 性能与稳定性提升

- 修复分享代码组件时超链接无法跳转的问题
- 修复部分场景复制代码组件报错的问题
- 修复选择 MCP 数量展示错误的问题
- 修复偶现的编辑器崩溃问题

🌟 用户体验改进

- 优化技能调用失败的信息展示
- 优化暗黑模式下的 Markdown 样式