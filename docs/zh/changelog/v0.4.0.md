# v0.4.0 更新日志

::: warning ⚠️ 重要通知：Refly 即将调整价格！现在购买，更划算！

📅 调整时间：
🕗 北京时间 3 月 16 日上午 8 点

📊 **新旧价格与使用额度对比**

| 套餐 | FREE | PLUS | PRO | MAX |
|------|------|------|-----|-----|
| 价格（调整后） | 0 | 月：**6.9$** <br/> 年：**69$** | 月：**12.9$** <br/> 年：**129$** | 月：**24.9$** <br/> 年：**249$** |
| 价格（调整前） | 0 | 月：4.9$ <br/> 年：24.5$ | 月：9.9$ <br/> 年：49.5$ | 月：19.9$ <br/> 年：99.5$ |
| 高阶模型（调整后） | ✅ 3 次/天 | ⚡️ **100 次/月** | ⚡️ **300 次/月** | ⚡️ **800 次/月** |
| 高阶模型（调整前） | ✅ 3 次/天 | ⚡️ 100 次/月 | ⚡️ 300 次/月 | ♾️ 无限 |
| 基础模型（调整后） | ✅ 30 次/天 | ⚡️ **1500 次/月** | ⚡️ **3000 次/月** | ⚡️ **8000 次/月** |
| 基础模型（调整前） | ✅ 30 次/天 | ⚡️ 1500 次/月 | ♾️ 无限 | ♾️ 无限 |

🎁 **现在购买的独家优势：**

✅ 锁定当前价格，未来续费也不会涨！
✅ 保留现有的高额度使用权！

> 举例说明：
> - ✨ 小明在 3 月 16 日前购买了 MAX 年度会员（99.5$/年）并开启了自动续费，那么他在第二年、第三年及后续年份都将继续享受 99.5$/年的价格，同时保留无限次数的高阶模型使用权限。
> - ⚠️ 小红在 3 月 16 日前购买了 MAX 年度会员但取消了自动续费，那么等到明年需要续费时，将需要按照新价格（249$/年）重新订阅，且仅享有新套餐内的使用配额（每月 800 次高阶模型）。

💡 简单来说：
现在购买，更划算、更自由、功能更多！ 🚀

> 🚀 **想了解 Refly 的更多未来计划?**
> 
> 我们正在开发许多激动人心的新功能。查看我们的[完整产品路线图](/zh/roadmap)了解更多详情。
:::

## 🦹 总结

🎉 Refly 史上最强更新！全新代码组件生成/原型图一键还原、画布分享、自定义提示词功能，搜索速度大幅提升，文件上传容量翻倍。构建更强大的 AI 创作引擎，让你的创意工作效率更上一层楼！

## ☄️ 私有部署优化

> [#527](https://github.com/refly-ai/refly/pull/527), [#542](https://github.com/refly-ai/refly/pull/542)

- 本地支持使用 Ollama！并提供[完整教程](https://docs.refly.ai/zh/guide/self-deploy/ollama)
- 极大优化本地部署的登录使用体验
- 移除本地部署里面的订阅、无关登录组件，提供更加纯净的本地部署使用体验
- 支持反向代理能力，无痛部署在线上云服务
- 支持本地部署可以展示和处理静态资源，包括图片、文件等能力
- 支持便捷切换文件处理解析器，包括 pdfjs 和 marker
- 支持展示本地执行错误栈，方便定位调试与反馈问题

## 🌟 新功能支持

- **正式推出代码小组件（Code Artifacts）**([#546](https://github.com/refly-ai/refly/pull/546))
  - 新增「小组件生成」技能，支持 AI 生成代码组件，实时预览运行结果
  - 支持 svg、mermaid、html、react 等多种主流代码的生成、展示、下载与分享
  - svg、mermaid 等支持直接下载图片或复制到剪切板，快速分享成果
  - 代码能够作为上下文被二次提问修改，或者手工修改调优然后预览最终结果
  - Markdown 里面的 Mermaid 也可以支持下载图片的操作按钮
- **画布分享与复制** ([#536](https://github.com/refly-ai/refly/pull/536), [#544](https://github.com/refly-ai/refly/pull/544), [#547](https://github.com/refly-ai/refly/pull/547))
  - 所有画布均可分享为公开链接，支持只读模式下查看
  - 支持一键复制其他人分享的画布，后续可任意修改和调整
  - 画布支持导出为图片
  - 欢迎大家给我们提交画布案例，后续我们会作为官方案例+署名展示给 Refly 所有的用户
- **支持自定义提示词** ([#533](https://github.com/refly-ai/refly/pull/533), [#539](https://github.com/refly-ai/refly/pull/539))
  - 新增「自定义提示词」技能，可以任意配置系统提示词、Temperature、Top P 等参数
  - 自定义提示词运行结果支持调整系统提示词等参数后重新运行
  - 支持克隆自定义提示词节点提问，并修改系统提示词后执行
  - 自定义提示词支持追问跟随已设置的提示词，不用频繁添加
- **联网和知识库搜索大幅优化** ([#523](https://github.com/refly-ai/refly/pull/523), [#533](https://github.com/refly-ai/refly/pull/533), [#538](https://github.com/refly-ai/refly/pull/538))
  - 优化查询预处理逻辑，支持传入画布任意上下文提问
  - 调整结果生成逻辑，优先回答问题而非简单引用搜索结果
  - 提升技能执行速度，大幅减少搜索卡死或者失败的情况
  - 提升意图理解能力，大幅度优化复杂提问的可用度
- **提问链接支持自动解析** ([#535](https://github.com/refly-ai/refly/pull/535), [#545](https://github.com/refly-ai/refly/pull/545))
  - 在输入框中直接粘贴网页链接，无需导入资源即可提问并获得答案
  - 结果生成后可以查看链接网页内容，并一键导入知识库
  - 支持所有的技能可以使用
- **全新的网站节点和代码节点** ([#546](https://github.com/refly-ai/refly/pull/546))
  - 支持在画布中添加自定义的网站并渲染，比如 B 站视频、Medium 文章、Github 仓库等
  - 支持对网站节点一键追问问 AI 进行内容总结
  - 代码节点可以预览 SVG、HTML、React、Mermaid、HTML 等能力、支持修改代码
  - 代码节点支持选为上下文结合 Artifact 技能进行迭代优化代码
- **文件上传限制调整** ([#524](https://github.com/refly-ai/refly/pull/524))
  - 付费用户的文件上传限制大幅提升
  - Plus 用户最多可上传 10MB，Pro 用户最多可上传 20MB，Max 用户最多可上传 30MB
  - 后续随运营状况会逐步放开
- **支持自定义节点连接线** ([#522](https://github.com/refly-ai/refly/pull/522), [#529](https://github.com/refly-ai/refly/pull/529))
  - 现在可以直接从自由节点中拉线链接其他节点
  - 任意连接线可以删除
  - 可以对连接线编写注释
  - 针对 AI 提问节点，将上下文节点连线到提问节点自动将上下文添加到提问框
- **更专业的网站预览能力** ([#548](https://github.com/refly-ai/refly/pull/548))
  - 支持落地页在社交网络中展示图片/标题和介绍
  - 支持自动根据画布的状态生成预览图，优化画布列表页面展示
- **支持推理模型的思维过程展示** ([#520](https://github.com/refly-ai/refly/pull/520), [#534](https://github.com/refly-ai/refly/pull/534))
  - 支持 Claude 3.7 Sonnet Thinking、DeepSeek R1 的思维链流式展示
  - 支持折叠思维链

## 💫 核心问题优化

- 账户设置支持修改头像 ([#556](https://github.com/refly-ai/refly/pull/556))
- 紧凑模式默认打开，并在后续提问时自动保持
- 修复重新运行技能时选中技能丢失的问题 ([#532](https://github.com/refly-ai/refly/pull/532))
- 修复画布中由于嵌套重复的上下文导致卡顿甚至崩溃的问题 ([#552](https://github.com/refly-ai/refly/pull/552))
- 备忘录节点支持修改文本颜色的菜单 ([#553](https://github.com/refly-ai/refly/pull/553))
- 节点预览卡片支持修改标题
- 文档编辑器补充多语言翻译 ([#543](https://github.com/refly-ai/refly/pull/543))
- 优化回答节点底部模型名称太长时挤压问题
- 优化空画布自动生成名称的效果问题 ([#537](https://github.com/refly-ai/refly/pull/537))
- 修复折叠模式跟随问题，设置全局节点折叠或展开之后，后续节点都跟随这个配置 ([#546](https://github.com/refly-ai/refly/pull/546))
- 支持默认画布不自动布局，修复节点布局混乱的问题
- 移除暗黑模式的处理 ([#531](https://github.com/refly-ai/refly/pull/531))
