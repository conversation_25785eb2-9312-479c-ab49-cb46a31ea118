# 画布与节点

## 画布的概念介绍

### 画布是什么

*   画布是系统中的核心工作区域，它是一个可视化业务构建环境，允许用戶通过拖拽、连接不同节点来构建和编排功能流程。画布提供了直观的图形化界面，使用戶能够以可视化方式设计复杂的业务逻辑。每个画布代表一个独立的功能模块或流程，可以包含多种节点并通过连线定义它们之间的关系和数据流向。

### 画布在整个系统的作用

*   画布本质上是系统的"构建基石"，通过提供模块化、可视化的开发方式，使用戶能够高效地设计、实现和管理复杂功能，同时保持系统的灵活性和可扩展性。
*   画布在系统中扮演着以下关键⻆色：
    *   功能模块化：将复杂系统分解为可管理的独立模块，每个画布专注于特定功能
    *   可视化编排：提供直观的方式设计和编排业务流程，降低技术⻔槛
    *   资源共享与复用：一个画布中创建的资源可被其他画布调用，促进模块间的协作与集成
    *   逻辑抽象：将复杂的业务逻辑转化为可视化流程，便于理解和维护
    *   快速迭代：支持快速构建、测试和调整功能模块，加速开发周期
    *   系统整合：作为连接不同功能组件的桥梁，实现系统各部分的有机整合

## 节点（Nodes）概述

*   节点是画布中的基本构建单元，它们代表不同类型的资源，可以通过连线进行交互和数据流动。节点系统提供了一种直观的可视化方式，让用戶能够设计和构建复杂的工作流程。

## 节点的关键特性

*   多样性：包括资源节点、工具节点、LLM节点等多种类型，每种类型具有特定功能
*   连接性：节点之间可以通过连线建立关系，形成数据流或逻辑流
*   模块化：每个节点都是独立的功能单元，可以自由组合构建复杂系统
*   可视化：通过画布直观呈现工作流程和资源关系，提升用戶体验

节点本质上是功能和数据的封装，通过节点间的连接，用戶可以创建从简单到复杂的各种逻辑关系，实现资源间的无缝协作和信息传递，为业务流程提供可视化编排能力。

## 节点操作

*   新建节点->Refly中每个单元都是一个节点,你可以通过鼠标点击来创建你要的任何功能节点(具体节点的功能可以参考文档后续部分)
    如下图任意一个创建出来的内容都是一个节点
    ![图1](/images/2025-04-26-16-12-55.webp)

*   节点操作->当你在节点上面点击就就打开节点显示,显示出出现在画布的右侧如图右上⻆分别为:适合阅读,最大化,锁定,扩展(定位节点,添加到上下文,删除节点),关闭节点显示
    定位节点:点击后可以在画布中定位到这个显示由画布中那个节点提供.
    添加到上下文:
    删除节点:可以通过这个按钮删除,同时会删除画布中对应节点.(注意:节点删除是不可逆操作请谨慎操作)
    ![图1](/images/2025-04-26-16-13-09.webp)

*   节点操作(画布上右键节点)->针对于不同类型的节点右键后功能会有差异具体功能请参考文档后对具体功能介绍,但是每个节点右键后都有删除选项,在这里也可以进行节点删除(注意:节点删除是不可逆操作请谨慎操作)
    ![图1](/images/2025-04-26-16-13-20.webp)
    ![图2](/images/2025-04-26-16-13-28.webp)

## 画布区域介绍

### 画布浏览

#### 快速浏览:
画布的快速浏览默认会放置你最后时间段编辑的画布默认会显示4个画布信息.
![图1](/images/2025-04-26-16-14-04.webp)

#### 展开所有画布:
可以通过展开所有画布按钮来查看自己创建和管理的所有历史画布,可以通过滚动的方式来进行浏览选择.
步骤1:点击画布如图1红色框
![图1](/images/2025-04-26-16-14-27.webp)
步骤2:可以通过鼠标滚动来选择所需要的历史画布,如图2,可以针对画布进行继续对话(进入画布,或者直接点击画布),重命名,复制画布,删除画布(重命名,复制画布,删除画布在后面章节中介绍)
![图2](/images/2025-04-26-16-14-40.webp)
#### 新建画布:
创建画布,可以通过点击新建画布按钮来实现
![图1](/images/2025-04-26-16-14-54.webp)
### 画布操作

#### 重命名:
将现有画布重新命名(命名是逻辑操作,你在其他资源中引用的画布名称的资源也会对应改变,不会影响你之前的业务资源引用)
![图1](/images/2025-04-26-16-15-13.webp)
![图2](/images/2025-04-26-16-15-23.webp)
#### 复制画布:
将现有画布复制一份作为新的画布(选项:复制该画布所包含的文档以及资源)
![图1](/images/2025-04-26-16-15-32.webp)
![图2](/images/2025-04-26-16-15-40.webp)
#### 删除画布:
画布和画布内的流程节点将被删除,画布内的资源节点取决于你是否选择(同时删除画布中的资源和文档)(注意:画布删除是不可逆操作请谨慎操作)
![图1](/images/2025-04-26-16-15-50.webp)
![图2](/images/2025-04-26-16-15-58.webp)