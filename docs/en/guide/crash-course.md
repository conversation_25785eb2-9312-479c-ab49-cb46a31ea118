# Crash Course

Let's understand how Refly works at its core through a user story, such as creating an article about the newly released "OpenAI Sora".

## Register/Login

First, visit https://refly.ai/, and complete account registration or login by logging in and authorizing via email, Google, or GitHub.

## Organizing Creative Ideas with Free Canvas

After entering the Refly app, you can generate various questions about Sora-related technologies on Refly's **free canvas**, then understand the implementation principles of each part. Once you have a basic understanding of Sora's technology and product, you can continue to ask follow-up questions based on certain node topics, dive deeper, and understand more advanced concepts step by step.

What's more amazing is that each step represents your free-flowing questioning and thinking process, and these processes don't interfere with each other unless you manually ask questions based on a certain node or add it to the context (**"Add to Context/Follow-up" floating on each node**). This provides you with **the ability to ask in-depth questions based on a specific topic or think freely based on multiple topics**, which is very flexible!

![generate-outline](/images/generate-outline.webp)

## Using Built-in AI Search or Uploading Writing Materials

Once you have a general understanding of Sora's principles and derived technologies, you can start preparing your creation materials! Click the floating button on the left side of Refly, select the second button "Add Resource", and you can add interesting materials to the free canvas through **online search**, copying webpage links, or plain text for later research.

Each added material is automatically processed with AI semantics, allowing you to use the "Knowledge Base Search" skill to semantically search and question all resources - it's like having an AI search engine like Perplexity powered by both web content and your private domain knowledge!

![explore](/images/explore.webp)

## Multi-threaded Conversations for Creative Research

After importing your writing materials, you can ask AI questions based on the materials, such as **summarizing, translating, or generating tables for certain materials**. Use the first button "Selection Mode" on the left of the canvas or the "Add to Context/Follow-up" button floating on each node to **select multiple materials** for summarization, translation, or ask any questions you want!

Of course, throughout the research process, Refly also provides many useful skills such as "Web Search" and "Knowledge Base Search" that can be called with one click to perform **intelligent semantic search** on all related content across the web or all materials you've already saved - don't miss any detail!

![research](/images/research.webp)

## Using AI Editor for Writing

Finally, we come to the most exciting part! You can quickly generate a document using the "Generate Document" skill or create a new document using the "New Document" in the sidebar. Most usefully, during the document generation process, you can select any materials from the canvas (just click the "Add to Context/Follow-up" button floating on each node), combine materials with questions to generate documents, making your document content specifically tailored to your writing goals!

Of course, you can also independently complete each AI question or in-depth exploration based on topics, then use the "Insert" button on the answer node to insert useful content into your article with one click, step by step generating a usable document with AI assistance!

At this point, you might ask, if each step is generated by AI, won't the overall result be messy? Don't worry! Refly's document editor comes with rich text operation capabilities, allowing you to quickly format using the provided rich text tools. Of course, it also supports selecting content with one click to let AI format or edit for you - how thoughtful is that! 😆

![generate-article](/images/generate-article.webp)

That's it! Through the steps above, you've mastered the secret to using Refly AI to help you efficiently complete the journey from creative ideas to high-quality content! Now it's time for you to explore freely ~ Good luck 🍀

## FAQ

### Issue Reporting

Currently, Refly is fully open and in its early access phase, so you might encounter some usability issues, but we'll fix them right away! If you encounter any errors or other issues, you can copy the troubleshooting code and throw it in our face immediately! We'll follow up with fixes right away! For example, the error below, and traceID is the error code.

![error-report](/images/error-report.webp)

### Landing Page Error

![landing-page-error](/images/landing-page-error.webp)

This situation occurs because Refly's overall login authorization system has changed. Users who previously participated in the beta test and were already logged in will encounter this - just click "Get Started" to log in normally.

## Get Help & Join Feedback Group

You can visit the [Contact Us](/community/contact-us.md) link to join our community, and we'll answer your questions ASAP.
