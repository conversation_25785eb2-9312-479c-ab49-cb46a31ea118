# Refly AI Chrome Extension Guide

The Refly Chrome extension is a powerful personal content management tool that helps you easily collect, organize, and save valuable content from any webpage to your knowledge base. Whether it's article paragraphs, important notes, or complete webpages, you can save them with one click and build your second brain.

## Installation

1. Visit the [Chrome Web Store](https://chromewebstore.google.com/detail/lecbjbapfkinmikhadakbclblnemmjpd)
2. Click the "Add to Chrome" button
3. Confirm the installation when prompted

## Features

### 1. Webpage Content Saving Modes

#### 1.1 Text Selection Saving
- Find the Refly floating ball on the right side of the webpage
- Enable selection mode to choose content you want to save
- After selection, choose to save or clip to your knowledge base
- Support multiple selections and cumulative saving

<div style="padding: 20px 0;">
<video width="100%" style="border-radius: 10px;" autoplay loop muted playsinline>
  <source src="https://static.refly.ai/extension/text_selection_clip.mp4" type="video/mp4">
</video>
</div>

#### 1.2 One-Click Page Saving
- Save the entire webpage content through the right-side floating ball
- Automatically extract main content, filtering out ads and irrelevant information
- Preserve original formatting and images

<div style="padding: 20px 0;">
<video width="100%" style="border-radius: 10px;" autoplay loop muted playsinline>
  <source src="https://static.refly.ai/extension/whole_page_clip.mp4" type="video/mp4">
</video>
</div>

#### 1.3 Popup Window Operations
- Click the Refly icon in the browser's top-right corner
- View and edit content to be saved in the popup window
- Support content preview and quick editing

<div style="padding: 20px 0;">
<video width="100%" style="border-radius: 10px;" autoplay loop muted playsinline>
  <source src="https://static.refly.ai/extension/popup_clip.mp4" type="video/mp4">
</video>
</div>

#### 1.4 Clipboard Enhancement
- Support copying any content to clipboard
- Paste and supplement content in the popup window
- Support rich text format saving

<div style="padding: 20px 0;">
<video width="100%" style="border-radius: 10px;" autoplay loop muted playsinline>
  <source src="https://static.refly.ai/extension/clipboard_clip_and_save.mp4" type="video/mp4">
</video>
</div>

### 2. Knowledge Base Integration

#### Content Processing Flow
1. Clipped content is automatically saved to your knowledge base
2. Support converting saved content into document format
3. Edit, organize, and tag saved content
4. Support selecting partial content for note creation or AI queries

## Supported Platforms

Refly supports content saving from various popular platforms, including:

### Social Media
- [Twitter/X](https://twitter.com)
- [Reddit](https://reddit.com)
- [LinkedIn](https://www.linkedin.com)
- [Xiaohongshu](https://xiaohongshu.com/explore)
- [Zhihu](https://www.zhihu.com)
- [Douban](https://www.douban.com)
- [Juejin](https://juejin.cn)

### Academic Research
- [Google Scholar](https://scholar.google.com)
- [arXiv](https://arxiv.org)
- [ResearchGate](https://www.researchgate.net)
- [SSRN](https://www.ssrn.com)

### Tech Communities
- [GitHub](https://github.com)
- [Stack Overflow](https://stackoverflow.com)
- [Dev.to](https://dev.to)
- [HackerNews](https://news.ycombinator.com)

### Productivity Tools
- [Notion](https://notion.so)
- [Obsidian](https://obsidian.md)
- [Logseq](https://logseq.com)
- [Readwise](https://readwise.io)

### Others

These are just some of the main platforms. Refly supports saving content from any webpage, allowing you to save whatever interests you.

## Best Practices

1. **Selective Saving**: Only save content that's valuable to you, avoid information overload
2. **Timely Organization**: Regularly organize saved content to build your knowledge system
3. **Privacy Protection**: Your data is securely encrypted and stored
4. **Regular Review**: Periodically review and organize your saved content

## Troubleshooting

If you encounter any issues:

1. Ensure the extension is up to date
2. Check browser compatibility
3. Clear browser cache
4. Restart the browser
5. Contact support team if problems persist

## Getting Help

If you need assistance:

- Visit our [Community page](/community/contact-us)
- Check the [Changelog](/changelog/v0.2.4) for latest updates
- Join our Discord community for real-time support
- Contact our support team for technical issues 