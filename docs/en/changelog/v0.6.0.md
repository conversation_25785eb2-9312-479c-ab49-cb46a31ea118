# v0.6.0 Release Notes

## 🚀 Summary

**Refly Major Update: Added custom model configuration, one-click cloud deployment, redesigned homepage, presentation functionality, batch image processing and document export features, while optimizing performance and user experience to create a more efficient and stable AI-native creation platform!**

## 🌟 New Features

- **🔑 Custom Model & Provider Configuration (BYOK)**
    - 💰 Use Refly completely free: Connect your own API Key with no subscription quota consumption
    - 🛠️ Comprehensive customization support: 
        - 🤖 Support for custom providers for conversation models, embedding models, web search, and resource parsing
        - 📋 Conversation models support custom lists and grouping (e.g., specialized models for coding, writing)
        - ⚙️ Fine-grained parameter adjustments for embedding and reranking models
- **🚢 One-Click Cloud Deployment**
    - 💻 Zero-cost deployment: Support for instant deployment on Gitpod, Zeablur, and Sealos platforms
    - ⚡ Ready to use: Quick access to all Refly canvas capabilities without complex configuration
- **🏠 Redesigned Homepage Experience**
    - 🔍 Smart quick access: Support for direct questions on the homepage with one-click canvas creation
    - 📚 Scenario-based template library: Rich quick-start prompts for common scenarios and curated community templates
- **📊 Presentation Functionality**
    - 🧩 Flexible content organization: Add any canvas node to presentations and arrange as needed
    - 🎭 Professional presentation mode: Support for full-screen presentations
    - 🔗 Easy sharing: One-click generation of public sharing links for team collaboration
- **📸 Enhanced Image Processing**
    - 📤 Batch uploads: Support for copying, dragging, or clicking to upload multiple images simultaneously
    - 📥 Multiple addition methods: 
        - 📁 Direct upload from the resource panel to add to canvas
        - 🖱️ Drag multiple images to canvas for automatic upload
        - 📋 Copy multiple images and paste directly to canvas
    - 🧑‍🎨 Support for image generation skills (currently requires your own API Key configuration)
- **📄 Document Export Functionality**
    - 📑 Multiple format support: Export content to both docx and pdf formats
    - 🎨 Format preservation: Complete retention of document styles and structure

## 💫 System Optimization and Bug Fixes

- **🚀 Performance and Stability Improvements**
    - 🔄 Resolved lag and crash issues caused by high resource usage during AI responses
    - 🔁 Fixed abnormal AI response behavior when copying shared canvases
    - 💾 Optimized local caching mechanism to prevent crashes due to cache overflow
    - 🖼️ Fixed issues with opening dialogs when canvas is empty
    - 🔌 Resolved occasional node connection line disappearance and related performance optimizations
- **🌟 User Experience Enhancements**
    - 📂 Optimized sidebar interaction with collapsible canvas and knowledge base sections
    - 📚 Knowledge base improvements: 
        - 💬 Separate settings for knowledge base prompts and retrieval
        - 🆕 Intuitive add entry points for empty knowledge bases
    - 🔄 Simplified node operations: 
        - ➕ Added "+" symbol for intuitive guidance
        - 👆 Improved hints for dragging connections and adding nodes
        - 🖱️ Optimized right-click menu to reduce operation steps
    - 📐 Improved automatic layout of child nodes within groups to maintain relative positions and avoid overlapping
    - 📜 Linear conversation optimization with automatic scrolling to initial position when opening responses
    - 🔍 Support for multi-preview window selection menu with direct AI question node creation
    - 💻 Optimized code component handling to resolve truncation issues with large code blocks
    - ⌨️ Editor loading optimization with automatic fallback on timeout
    - 📝 Improved friendly prompts for various operations

---

🙏 We sincerely thank all users for their support and feedback. The Refly team will continue to optimize the product experience and provide you with more powerful and intelligent tools for creation and knowledge management. If you have any questions or suggestions, please feel free to contact us! 