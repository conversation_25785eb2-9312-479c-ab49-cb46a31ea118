# v0.2.1 Release Notes

## 🦹 Summary

Refly has released 5+ new features in version 0.2.1, optimized 10+ core key issues, and resolved 7+ basic problems/Bugs. The core focus of this release is: **the official launch of tiered paid subscription features (Free/Pro/Max), with a 2-month gift for the first year and an additional 50% discount 🔥, unlimited use of Claude 3.5 Sonnet/GPT-4o for just $8.3 per month, time is limited, so those who need it can hurry up and subscribe 🐛;** at the same time, the AI interaction capabilities of Memo nodes have been significantly enhanced, multiple user experiences have been optimized (such as multi-select nodes, canvas renaming, etc.), and key issues such as login status, network connection, and white screens have been fixed.

## **🌟** New Feature Support

- **Official Launch of Paid Subscription Features with 50% Discount for the First Year 🔥**
  - Two subscription plans, Pro and Max, are available. The Max plan offers unlimited use of all models.
  - A limited-time 50% discount for the first year (must be paid annually), with a monthly cost of only $8.3.
  - Payment methods include credit card and Alipay.
- **Memo Experience Upgrade:**
  - Supports creating empty Memo nodes for note-taking based on any node.
  - Supports selecting all Memo nodes on the canvas from the context answer list.
  - Supports directly asking AI questions to Memo nodes or adding them as question context.
- **Enhanced Mouse Shortcut + Box Selection for Multi-Node Selection Experience:**
  - Supports multi-selecting nodes using Shift/Meta + click.
  - Supports multi-selecting nodes using Shift/Meta + click while already box-selecting.
- **More Convenient Node Menus:**
  - AI answer node menus support quick modification of question modes.
- **Canvas Renaming Experience Optimization:**
  - Supports direct renaming of canvases in the sidebar canvas list.
  - Supports renaming canvases from the "More" menu in the upper right corner of the open canvas.
  - Added renaming guidance prompts to the canvas name in the upper left corner.

## **💫** Core Issue Optimization

- Optimized login status retention to prevent automatic logout during continuous use.
- When creating a Memo based on an AI answer/resource, adding a connection from this node enhances dependency relationships.
- Optimized the issue of network connection timeouts during network fluctuations.
- Supports creating document copy nodes.
- Supports operating the left canvas nodes while the search result drawer is open.
- Optimized the model selection experience for skill nodes, with new skill nodes following the previous model selection.
- Supports sliding to select content or scrolling node content by clicking a node again after the initial click.
- Automatically focuses on the input box when creating an AI node, accelerating the Ask AI question experience.
- Supports switching skills using / at any position in the input box.
- Fixed the issue where AI answers were reset after re-editing and running.

## **🐞** Other Bug Fixes or Optimizations

- Fixed the occasional white screen issue during product use.
- Optimized the retention of question input content when switching canvases.
- Adjusted the order of the zoom in and zoom out buttons in the lower left corner.
- Optimized interface scrolling issues when the browser window height is low.
- Optimized abnormal reactions when using up/down keys and enter in the input box.
- Defaulted to mouse mode instead of trackpad mode.
- Fixed the issue where all connected edges were deleted after modifying the Prompt.
- Supported hover display of node titles when they exceed the container size.
