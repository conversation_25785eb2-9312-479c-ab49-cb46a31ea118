# v0.7.1 Release Notes

## 🌈 Highlights

This update proudly introduces the MCP Store, allowing you to easily connect to five major platforms like GitHub and Zapier just by configuring their API Keys. What's even better, Skill Invocations now support background execution, so your tasks can continue running even after you close the browser. Additionally, this version includes multiple bug fixes and optimizations for improved system stability and user experience.

## 🌟 New Features

- ⚙️ **Introducing the MCP Store for quick addition of MCP Servers.**
  - Initially launching with 5 MCP Servers: GitHub, Firecrawl, DeepWiki, Gitee, and Zapier.
  - Simply configure the API Key for the respective platform to connect and start using it.
- **Background execution for Skill Invocations.**
  - You can now close your browser, and the skill invocation will continue to run in the background. You can return to check the results at any time.

## 🛠️ System Optimizations & Bug Fixes

🚀 **Performance & Stability Improvements**

- Fixed an issue where hyperlinks were not clickable in shared code components.
- Fixed an error that occurred when copying code components in certain scenarios.
- Fixed an incorrect count display for selected MCPs.
- Fixed occasional editor crashes.

🌟 **User Experience Improvements**

- Improved the error message display for failed Skill Invocations.
- Optimized Markdown styling in dark mode.