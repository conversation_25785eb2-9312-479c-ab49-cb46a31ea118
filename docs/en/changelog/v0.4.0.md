# v0.4.0 Release Notes

::: warning ⚠️ Important Notice: Refly Price Adjustment Coming Soon! Buy Now for Better Value!

📅 Adjustment Time:
🕗 March 16th, 00:00 UTC

📊 **New vs Current Pricing and Usage Quota Comparison**

| Plan | FREE | PLUS | PRO | MAX |
|------|------|------|-----|-----|
| Price (After Adjustment) | 0 | Monthly: **$6.9** <br/> Annual: **$69** | Monthly: **$12.9** <br/> Annual: **$129** | Monthly: **$24.9** <br/> Annual: **$249** |
| Price (Before Adjustment) | 0 | Monthly: $4.9 <br/> Annual: $24.5 | Monthly: $9.9 <br/> Annual: $49.5 | Monthly: $19.9 <br/> Annual: $99.5 |
| Advanced Models (After) | ✅ 3/day | ⚡️ **100/month** | ⚡️ **300/month** | ⚡️ **800/month** |
| Advanced Models (Before) | ✅ 3/day | ⚡️ 100/month | ⚡️ 300/month | ♾️ Unlimited |
| Basic Models (After) | ✅ 30/day | ⚡️ **1500/month** | ⚡️ **3000/month** | ⚡️ **8000/month** |
| Basic Models (Before) | ✅ 30/day | ⚡️ 1500/month | ♾️ Unlimited | ♾️ Unlimited |

🎁 **Exclusive Benefits When You Buy Now:**

✅ Lock in current pricing for future renewals!
✅ Keep your current high usage quotas!

> Examples:
> - ✨ If Alice purchases a MAX annual subscription ($99.5/year) before March 16th and enables auto-renewal, she will continue to enjoy both the $99.5/year pricing and unlimited access to advanced models for the second year, third year, and all subsequent years.
> - ⚠️ If Bob purchases a MAX annual subscription before March 16th but cancels auto-renewal, when he needs to renew next year, he will need to subscribe at the new price ($249/year) and will only have access to the new plan's quota (800 advanced model uses per month).

💡 In Short:
Buy now for better value, more freedom, and enhanced features! 🚀

> 🚀 **Want to learn more about our future plans?**
> 
> We're working on many exciting new features. Check out our [complete product roadmap](/roadmap) for more details.
:::

## 🦹 Summary

🎉 Refly's strongest update ever! Brand new code component generation/one-click prototype restoration, canvas sharing, custom prompt functionality, greatly improved search speed, and doubled file upload capacity. Building a more powerful AI creation engine to take your creative work efficiency to the next level!

## ☄️ Improvements for Self-Deployment

> [#527](https://github.com/refly-ai/refly/pull/527), [#542](https://github.com/refly-ai/refly/pull/542)

- Local support for using Ollama with [complete tutorial](https://docs.refly.ai/guide/self-deploy/ollama)
- Greatly optimized login experience for local deployment
- Removed subscription and unrelated login components from local deployment, providing a cleaner local deployment experience
- Support for reverse proxy capabilities, painless deployment on cloud services
- Support for local deployment to display and process static resources, including images, files, and other capabilities
- Support for convenient switching of file processing parsers, including pdfjs and marker
- Support for displaying local execution error stacks for easy debugging and problem feedback

## 🌟 New Features

- **Official launch of Code Artifacts** ([#546](https://github.com/refly-ai/refly/pull/546))
  - Added "Code Artifacts" skill, supporting AI-generated code components with real-time preview of running results
  - Support for generation, display, download, and sharing of various mainstream code types including svg, mermaid, html, react
  - svg, mermaid, etc. support direct download as images or copying to clipboard for quick sharing
  - Code can be used as context for follow-up questions and modifications, or manually modified and optimized with preview of final results
  - Mermaid diagrams in Markdown also support image download operation buttons
- **Canvas Sharing and Copying** ([#536](https://github.com/refly-ai/refly/pull/536), [#544](https://github.com/refly-ai/refly/pull/544), [#547](https://github.com/refly-ai/refly/pull/547))
  - All canvases can be shared as public links, viewable in read-only mode
  - Support for one-click copying of canvases shared by others, with subsequent modifications and adjustments possible
  - Canvas supports export as images
  - We welcome everyone to submit canvas use cases, which we will later showcase as official examples with attribution to all Refly users
- **Support for Custom Prompts** ([#533](https://github.com/refly-ai/refly/pull/533), [#539](https://github.com/refly-ai/refly/pull/539))
  - Added "Custom Prompt" skill, allowing arbitrary configuration of system prompts, Temperature, Top P, and other parameters
  - Custom prompt execution results support adjusting system prompts and other parameters for re-running
  - Support for cloning custom prompt nodes for questions and modifying system prompts before execution
  - Custom prompts support follow-up questions with already set prompts, eliminating the need for frequent additions
- **Web and Knowledge Base Search Optimization** ([#523](https://github.com/refly-ai/refly/pull/523), [#533](https://github.com/refly-ai/refly/pull/533), [#538](https://github.com/refly-ai/refly/pull/538))
  - Optimized query preprocessing logic, supporting questions with any canvas context
  - Adjusted result generation logic to prioritize answering questions rather than simply referencing search results
  - Improved skill execution speed, greatly reducing search freezes or failures
  - Enhanced intent understanding capability, significantly optimizing usability for complex questions
- **Question Links Support Automatic Parsing** ([#535](https://github.com/refly-ai/refly/pull/535), [#545](https://github.com/refly-ai/refly/pull/545))
  - Directly paste website links in the input box to ask questions and get answers without importing resources
  - After results are generated, you can view link webpage content and import it to the knowledge base with one click
  - Supports all skills
- **Brand New Website Nodes and Code Nodes** ([#546](https://github.com/refly-ai/refly/pull/546))
  - Support for adding and rendering custom websites in the canvas, such as Bilibili videos, Medium articles, Github repositories, etc.
  - Support for one-click follow-up questions to AI for content summarization of website nodes
  - Code nodes can preview SVG, HTML, React, Mermaid, HTML capabilities and support code modifications
  - Code nodes can be selected as context with Artifact skills for iterative code optimization
- **File Upload Limit Adjustments** ([#524](https://github.com/refly-ai/refly/pull/524))
  - Significantly increased file upload limits for paid users
  - Plus users can upload up to 10MB, Pro users up to 20MB, Max users up to 30MB
  - Limits will be gradually relaxed depending on operational conditions
- **Support for Custom Node Connection Lines** ([#522](https://github.com/refly-ai/refly/pull/522), [#529](https://github.com/refly-ai/refly/pull/529))
  - Now you can directly draw lines from free nodes to connect other nodes
  - Any connection line can be deleted
  - Comments can be written for connection lines
  - For AI question nodes, connecting context nodes to question nodes automatically adds context to the question box
- **Professional Website Preview Capabilities** ([#548](https://github.com/refly-ai/refly/pull/548))
  - Support for landing pages to display images/titles and descriptions in social networks
  - Support for automatically generating preview images based on canvas status, optimizing canvas listing page display
- **Support for Displaying Inference Model Thinking Process** ([#520](https://github.com/refly-ai/refly/pull/520), [#534](https://github.com/refly-ai/refly/pull/534))
  - Support for streaming display of thinking chains for Claude 3.7 Sonnet Thinking and DeepSeek R1
  - Support for collapsing thinking chains

## 💫 Core Problem Optimization

- Account settings support avatar modification ([#556](https://github.com/refly-ai/refly/pull/556))
- Compact mode turned on by default and automatically maintained in subsequent questions
- Fixed issue of selected skills being lost when re-running skills ([#532](https://github.com/refly-ai/refly/pull/532))
- Fixed lag and even crash issues in canvas due to nested duplicate contexts ([#552](https://github.com/refly-ai/refly/pull/552))
- Memo nodes support text color modification menu ([#553](https://github.com/refly-ai/refly/pull/553))
- Node preview cards support title modification
- Document editor supplemented with multilingual translation ([#543](https://github.com/refly-ai/refly/pull/543))
- Optimized the issue of model names being compressed when too long at the bottom of answer nodes
- Optimized the effect of automatically generating names for empty canvases ([#537](https://github.com/refly-ai/refly/pull/537))
- Fixed collapsing mode follow-up issue; after setting global node collapse or expansion, subsequent nodes all follow this configuration ([#546](https://github.com/refly-ai/refly/pull/546))
- Support for default canvas without automatic layout, fixing node layout chaos issues
- Removed dark mode processing ([#531](https://github.com/refly-ai/refly/pull/531))
