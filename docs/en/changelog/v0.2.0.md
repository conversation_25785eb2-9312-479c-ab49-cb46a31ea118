# v0.2.0 Release Notes

## 🦹 Video Summary

**Refly is about to release a new major version v0.2.0, which supports 8+ significant new capabilities. It fully supports AI + canvas for a more intuitive and easy-to-understand operation experience, and greatly optimizes the canvas performance, aiming to match the usage experience of Native/local Apps.** Here are some introductions to the core capabilities:

## **🌟** New Feature Support

- **Canvas Adds Memo Node:**
  - Quickly record inspirations, supports writing Markdown, and allows free setting of background colors.
  - Supports quickly excerpting Memos from resources, documents, and AI answers.
  - One-click insertion into documents to assist content writing.
  - Focuses on lightweight recording, with a character limit.
- **Canvas Adds AI Question Node:**
  - Double-click on the blank area of the canvas to directly create an AI question node. After entering the question and context, run it to get the answer directly.
  - Supports quick follow-up questions on resources, documents, and AI answer nodes, adding AI question nodes and automatically associating context.
  - When following up on AI answer nodes, it can automatically trace the entire conversation history, avoiding messy connections.
  - Supports parallel output of multiple AI answers.
  - Supports selecting AI skills through `/` for convenient switching between web search, knowledge base search, and other skills.
- **Supports Star Open-Source Models:**
  - Adds well-known open-source models such as DeepSeek V3, Qwen, Llama, and Mistral.
  - All models can use context, generate documents, or perform web search/knowledge base search.
- **Adds Node Grouping and Batch Operation Capabilities:**
  - Supports grouping and ungrouping nodes.
  - Supports batch questioning, adding as context, or batch deleting a group of selected nodes.
  - Supports moving and organizing nodes as a whole within a Group.
  - Supports Group-level menu operations and right-click menus.
  - Supports deleting nodes with the Delete key, or batch deleting nodes.
- **Empty Canvas Guidance and Canvas Menu Optimization:**
  - Adds new empty canvas guidance capabilities, including quick asking of AI and creating documents.
  - Supports double-clicking to pop up the menu and creating AI question nodes at the clicked position, for operations such as uploading resources, adding resources/documents/Memos.
  - Supports right-click canvas operation menu, with options to configure the display of connections, AI input box, and opening or closing the click-to-open preview card.
  - Supports all nodes' thumbnail/adaptive display capabilities, supports global batch adaptive display and individual node adaptive display, supports node menu and right-click menu operations, enhancing canvas organization capabilities.
  - Supports searching nodes, and navigating nodes through shortcut keys up/down + enter.
- **Better Node Menu Guidance:**
  - Supports displaying node operation menus in a large card on the right, improving the visibility and guidance effect. Supports opening node operation menus with a right-click, supports product notification feedback after operations, and supports increasing node priority display on Hover, solving the problem of menus being covered.
  - Supports opening previews by clicking on nodes, and supports configuring whether to open previews with right-click menus.
  - Supports pinning preview cards, supports default opening of only one unpinned preview card, and does not open preview cards by default when adding resources/documents, reducing organizational burden.
  - Supports nodes handling layout, selection, and grouping operations for child nodes, enhancing the local organization capabilities of canvas nodes.
  - Supports reverse locating nodes on the canvas from the node preview menu.
  - Clicking on a node automatically scrolls the node preview list to the corresponding node.
- **Better AI Answer Processing:**
  - Supports modifying all configurable parameters such as Prompt and context model in AI answers and re-running them.
  - Supports cloning existing AI answer nodes, retaining questions and context, facilitating comparison of running results.
  - Supports invoking skill switching and adding input box prompts in Ask AI nodes and global AI input boxes through `/`, enhancing skill usage and convenient switching guidance, and supports switching skills through search.
- **Email Login Support:**
  - You can now register and log in directly with an email! Friends without Google or GitHub accounts, hurry up and try it!

## **💫** Core Issue Optimization

- **Better Canvas Performance and User Experience:**
  - Supports local caching of canvas and document data, providing a usage experience similar to local Apps, optimizing the opening speed of canvases and documents, and greatly improving the user experience (especially in poor network conditions). In addition, both documents and canvases support the display of synchronization status.
  - Optimizes the overall performance of the canvas, including issues such as node dragging, AI answer generation dragging, and canvas moving lag.
- **Better Node Automatic Layout Capability Support:**
  - Optimizes the issue of erratic positions when generating new answers and adding new nodes, automatically handling position layout to reduce the cost of manual node arrangement.
  - Supports node, group, or multiple selected node menus, including automatic layout of child nodes, selecting child nodes, and establishing groups of child nodes.
  - Automatically positions new nodes and optimizes their location in the upper left corner of the canvas to reduce occlusion issues.
- **More Consistent Context List Display:**
  - Unifies "conversation history" into the context for processing, reducing product usage friction caused by inconsistencies.
- **Better Reference Answer Capability and Display:**
  - Supports adding "references" from AI answers, resources, and documents to AI questions, and then combining other contexts for Ask AI.
  - Fixes the occasional issue of not selecting content when asking questions based on selected documents or resources.

## **🐞** Other Bug Fixes or Optimizations

- Optimizes the closing of node preview cards after deleting nodes with shortcut keys.
- Fixes the issue where knowledge base searches sometimes fail to find documents.
- Fixes the issue where canvas titles cannot be found through global search after updates.
- Optimizes the problem of answers not following user requirements when editing documents for Ask AI, and optimizes the token consumption of context.
- Optimizes the sidebar style and the synchronization issue between the sidebar and canvas titles.
- Node preview menus support multi-language translation.
- Optimizes the node style for skill responses.
- Optimizes the condition judgment for inserting and creating documents in AI answer nodes, graying out when not operable to reduce ambiguity.
