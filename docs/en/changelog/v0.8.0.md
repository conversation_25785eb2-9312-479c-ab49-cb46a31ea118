# v0.8.0 Release Notes

## 🌈 Highlights

Refly v0.8.0 marks a major upgrade in both perception capabilities and workflow completeness:

- Launch of the brand new Multimodal Engine — enabling seamless generation of images, videos, music, and voice
- Agents now support full background execution, so your tasks can continue even if you close the browser
- Introduction of a unified Model Supplier Store for managing 20+ AI model providers with one-click connectivity
- Major improvements to canvas sync, performance, and user experience throughout the system

## 🌟 New Features

- 🎥 **Multimodal Generation Fully Supported (Image / Video / Audio)**
  - Generate 🖼️ images, 🎬 videos, 🎧 music, and 🔊 voice directly within your workflow
  - Visual/audio results can be previewed live in the canvas and written to context for downstream use
  - Integrated with lyria-2 for dynamic BGM generation
  - Integrated with chatterbox for lifelike multilingual voice synthesis
- 🔁 **End-to-End Multimodal Flow Orchestration**
  - All generated media can be passed as structured variables to downstream nodes
  - Seamless integration with OCR, transcription, summarization, and rewriting nodes
  - Build a complete loop from one-line prompt to polished multimodal content
- 🤖 **Agent Background Execution**
  - Agents now support full background task execution
  - You can safely close your browser — your skills will continue running on the server
  - Revisit Refly any time to check results or outputs
- ⚙️ **New: Model Supplier Store**
  - A new unified panel to manage and configure 20+ model providers
  - Currently supports Replicate, OpenRouter, HuggingFace, and more
  - One-click connectivity testing for each provider after setup

## 🛠️ System Optimizations & Bug Fixes

🚀 **Performance & Stability Improvements**

- Rebuilt canvas sync logic to significantly reduce node disappearance or connection issues
- Fixed duplicate links in long-running AI conversations
- Corrected MCP selection count and visibility bugs

🌟 **User Experience Enhancements**

- Auto-naming support for generated code widgets when using skill creation
- Answer nodes now only display their current output — not full historical conversations

---

Let your workflows not only think — but also see, speak, and perform.

Refly v0.8.0 brings multimodal intelligence into every step of creation.

Let your ideas move. 🧠🎥🎶