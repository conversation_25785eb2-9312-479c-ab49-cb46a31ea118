# v0.2.2 Release Notes

## 🦹 Summary

Refly has introduced several important updates in version 0.2.2, including **product pricing, documentation experience, and core functionality optimizations**.

In terms of product pricing, we've launched a new tiered payment plan, including Plus, Pro, and Max tiers, **with the Plus plan starting at just $2 per month with annual billing and a 50% first-year discount**.

For user experience, we've introduced an internationalized documentation site and improved product onboarding, including new video introductions and optimized landing page core buttons.

Additionally, we've fixed and optimized several core issues, including improvements to Safari browser white screen issues, skill invocation-related bugs, document display, and login experience, making the product more smooth and stable.

## **🌟** New Feature Support

- **💰 New Plus Subscription Plan, Only $2/Month with Annual Billing ✌️**
  - Now offering three subscription tiers: Plus, Pro, and Max, with Max tier providing unlimited access to all models
  - Plus plan includes 1M advanced model and 5M basic model usage
  - First-year subscription (annual billing required) comes with 50% discount, only $2/month
  - Payment methods include credit card and Alipay
- **📚 New Documentation Site Support**
  - Internationalized documentation site with search, dark mode, and language switching
  - New privacy policy and user agreement, better suited for global users
- **🎯 Improved Onboarding Experience**
  - Added video introduction guide on landing page for quick understanding of Refly's core value
  - Optimized core button display experience on landing page
- **🔔 Enhanced Subscription Entry Points**
  - One-time reminder in the input box to guide users to subscribe
  - Added subscription entry point in sidebar settings

## **💫** Core Issue Optimizations

- 🌐 Optimized Safari browser experience, fixing occasional white screen issues
- 🔑 Fixed permission error issues during skill invocation
- 🔄 Fixed result overwrite issues during skill invocation
- 📄 Fixed document content not displaying after refresh and empty document preview issues
- ⚡️ Optimized model availability judgment when token usage is insufficient
- 🔒 Fixed automatic login issues after mailbox login
- 🔒 Fixed automatic login issues after mailbox registration or password reset
- ⚡️ Fixed immediate error on landing page entry
