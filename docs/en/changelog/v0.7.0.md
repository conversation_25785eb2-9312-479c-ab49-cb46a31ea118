# v0.7.0 Release Notes

## 🚀 Summary

We're thrilled to announce the official release of Refly v0.7.0, marking an exciting new chapter for our platform! 🎉

We are proud to launch the world's first open-source "Vibe Workflow" platform. You can now build and execute complex workflows simply by giving instructions in natural language, seamlessly taking you from initial concept to final delivery. Vibe Workflow integrates powerful AI Agents, configurable MCP servers, and a highly flexible free-form canvas with context selection. This unlocks a new paradigm in AI-driven automated creation, empowering you to explore infinite possibilities!

## 🌟 New Features

- 🤖 New Agent Mode (Beta)
    - **Intelligent Task Decomposition**: Automatically breaks down complex tasks into a series of executable sub-tasks.
    - **Automated Task Execution**: Executes sub-tasks by sequentially calling Refly's built-in skills or external tools via MCP, such as using a search engine, accessing a knowledge base, processing text, or generating code.
    - **Dynamic Evaluation & Re-planning**: Intelligently evaluates progress based on the results of each sub-task. If an obstacle is met or a better path is found, it dynamically adjusts the plan to ensure the final goal is reached.
    - **End-to-End Automation**: Delivers maximum automation, freeing you from tedious manual execution.
- ⚙️ MCP Server Configuration
    - **Custom MCP Server Support**: You can now configure third-party MCP servers compatible with SSE and Streamable protocols, significantly enhancing Refly's extensibility and versatility.
    - **Smart & Manual MCP Selection**: Manually specify which MCP to use in a conversation, or let the AI automatically select and invoke the most appropriate tool based on the context, offering both flexibility and intelligence.

## 🛠️ Improvements & Fixes

🚀 Performance & Stability

- Optimized the local caching mechanism to resolve crashes caused by cache overflow.
- Fixed an issue where the menu would unexpectedly close when switching skills.
- Fixed a bug where the default chat model setting would not take effect in some scenarios.

🌟 User Experience

- Overhauled Dark Mode for a more comfortable and consistent visual experience.
- Improved the layout and organization of the canvas node menu to make common actions more accessible.
- Reduced the frequent triggering of the full-screen loading indicator for a smoother experience.
- Improved the interaction design of the action buttons that appear below AI responses.