# v0.5.0 Release Notes

## 🚀 Summary

**Refly Major Update: Added 2+ open source contributors, 10+ practical templates, comprehensive upgrades to knowledge base partition management, linear conversations, mind maps, and document outline features, while optimizing canvas performance to support dozens of nodes with smooth operation, creating a more powerful AI-native creation platform!**

## 👨‍💻 New Open Source Contributors

We've welcomed 2+ new contributors in this update. Many thanks for their contributions to Refly 🎉 🎉 🎉

- **Anthhub** [GitHub](https://github.com/anthhub)
    - [PR #681](https://github.com/refly-ai/refly/pull/681)
    - [PR #681](https://github.com/refly-ai/refly/pull/681)
    - [PR #676](https://github.com/refly-ai/refly/pull/676)
- **pzc163** [GitHub](https://github.com/pzc163)
    - [PR #682](https://github.com/refly-ai/refly/pull/682)

## 📑 New Templates

We've added 10+ new templates covering training materials, mind map-generated drafts, business meeting theme readings, book breakdowns, Mermaid feature studies, social media cover designs, local deployment manual writing, recipe prompts, and more.

- **Training Materials**: [View Template](https://refly.ai/preview/canvas/can-jblcltgp6stbkp1x1u73ipqv)
- **Game Programming Challenge**: [View Template](https://refly.ai/preview/canvas/can-d1qabd0uyuc4mukirivsdsny)
- **Mind Map Generated Draft**: [View Template](https://refly.ai/preview/canvas/can-ewdi2tzzk0r7dqwub5hkbv0c)
- **Business Meeting Book Breakdown**: [View Template](https://refly.ai/preview/canvas/can-pkbqimy0jbkuo0tfolwczow9)
- **Social Media Cover**: [View Template](https://refly.ai/share/canvas/can-hcglpaf9e79rvxntp62zb4m5)
- **Recipe Prompts**: [View Template](https://refly.ai/share/canvas/can-rd5jivtn9q23uxjw3sx4u0on)
- **Technical Training Materials**: [View Template](https://refly.ai/share/canvas/can-rb0sotb7q4rhkbln6d9tsqj7)
- **Deployment Manual Writing**: [View Template](https://refly.ai/share/canvas/can-g04drhb8tr5kp0snimvb3x7m)
- **Mermaid Feature Research**: [View Template](https://refly.ai/share/canvas/can-pzeppylav0jgmu71zncfdnzb)
- **Investment Research Visualization**: [View Template](https://refly.ai/preview/canvas/can-dxwtqdkjbj5mdildpqqewc7m)

## 🌟 New Features

- **📚 Knowledge Base Partitioning (Similar to Claude Project Capability)**
    - Support for creating multiple knowledge bases to manage different knowledge and canvases in separate partitions
    - Each knowledge base supports custom prompt settings, allowing different customized experiences for different knowledge bases
    - When asking AI questions, users can select which knowledge base to mount, greatly enhancing the relevance and effectiveness of AI responses
- **💬 New Linear Conversation Support, Perfectly Adapting to Linear Sessions and Multi-threaded Canvas Conversations**
    - Support for global-level linear conversations, allowing continuous follow-up questions while maintaining context, similar to ChatGPT
    - Support for linear conversation with continuous follow-up questions on any AI response card
    - Support for adding context to selected linear conversation panels
- **🧠 New Mind Map Capabilities**
    - Now supports AI-combined context generation of NotebookLM-level mind maps
    - Mind maps support adding/deleting nodes, with Markdown-level operations for each node
    - Mind maps support one-click sharing and publishing
    - Especially suitable for book breakdowns, knowledge point organization, and similar scenarios
- **📋 New Document Outline Capability**
    - Support for displaying document table of contents outlines
- **📊 Support for New Mermaid Rendering**
    - Support for 22 types of diagrams including kanban boards, pie charts, entity relationship diagrams, and more
- **🔍 New Preview Panel Dragging and Widescreen Capabilities**
    - Support for widescreen mode in preview panels
    - Support for dragging and adjusting the position of preview panels
- **🎬 New Video Tutorial Display in Documentation**
    - Showcasing 10+ video tutorials currently recorded by the community [View Tutorials](https://docs.refly.ai/guide/video-tutorials)
- **✏️ Node Editing Enhancements**
    - All nodes can have editable titles for easy distinction and display
    - Support for alignment guides and grid snapping for canvas nodes, greatly optimizing canvas layout experience

## 💫 Core Problem Optimization

- 🛠️ Fixed issues with abnormal AI response count handling when copying canvases
- 🐳 Support for Docker local deployment with port conflict compatibility, fixing the issue of login entry not being displayed in local deployments
- ⏱️ Optimized AI response timeout issues, fixing screen flashing, content overlay, or error problems caused by long responses (time-consuming)
- 🔄 New detailed error display capability for model call errors, timeouts, and request rate limits
- 📋 Fixed the issue of only partial content being copied when duplicating nodes
- 📍 Optimized the positioning of images, resources, documents, etc. when adding them, defaulting to viewport placement
- 🚀 Further optimized performance for large canvases, now supporting dozens of nodes with smooth operation
